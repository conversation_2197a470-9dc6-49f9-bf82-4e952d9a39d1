import 'react-native-gesture-handler';
import React, { useContext } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AuthProvider } from './src/contexts/AuthContext';
import { ThemeProvider, ThemeContext } from './src/contexts/ThemeContext';
import AppNavigator from './src/navigation/AppNavigator';

/**
 * Mobile PFE App
 *
 * This version of the app:
 * - Connects to real backend_pfe API (https://127.0.0.1:8000/api)
 * - Uses JWT authentication (same as frontend_pfe)
 * - Includes real-time features with websockets
 * - Full apprenant interface functionality
 * - Supports dark mode and light mode
 */

// Main app component with theme context
const Main = () => {
  const { theme, isDarkMode } = useContext(ThemeContext);

  return (
    <>
      <AppNavigator />
      <StatusBar style={theme.statusBar} />
    </>
  );
};

export default function App() {
  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <AuthProvider>
          <Main />
        </AuthProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}
