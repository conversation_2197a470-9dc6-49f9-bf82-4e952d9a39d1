import React, { createContext, useState, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define theme colors - Matched with web frontend
export const lightTheme = {
  // Background colors
  background: '#F9FAFB', // slate-100
  card: '#FFFFFF',
  primary: '#4F46E5', // indigo-600
  secondary: '#10B981', // emerald-500
  accent: '#F59E0B', // amber-500
  error: '#EF4444', // red-500

  // Text colors
  text: {
    primary: '#1F2937', // gray-800
    secondary: '#6B7280', // gray-500
    tertiary: '#9CA3AF', // gray-400
    inverse: '#FFFFFF', // white
    accent: '#4F46E5', // indigo-600
  },

  // Input colors
  input: {
    background: '#F9FAFB', // slate-50
    border: '#E5E7EB', // gray-200
    placeholder: '#9CA3AF', // gray-400
    text: '#1F2937', // gray-800
  },

  // Border colors
  border: '#E5E7EB', // gray-200

  // Status colors
  success: '#10B981', // emerald-500
  warning: '#F59E0B', // amber-500
  danger: '#EF4444', // red-500
  info: '#3B82F6', // blue-500

  // Component specific
  statusBar: 'dark-content',
  tabBar: '#FFFFFF',
  tabBarInactive: '#6B7280', // gray-500
  tabBarActive: '#4F46E5', // indigo-600
  shadow: 'rgba(0, 0, 0, 0.1)',

  // Gradients
  gradients: {
    primary: ['#4F46E5', '#6366F1'], // indigo-600 to indigo-500
    secondary: ['#10B981', '#34D399'], // emerald-500 to emerald-400
  },
};

export const darkTheme = {
  // Background colors
  background: '#111827', // slate-950
  card: '#1F2937', // slate-900
  primary: '#6366F1', // indigo-500
  secondary: '#34D399', // emerald-400
  accent: '#FBBF24', // amber-400
  error: '#F87171', // red-400

  // Text colors
  text: {
    primary: '#F9FAFB', // gray-50
    secondary: '#E5E7EB', // gray-200
    tertiary: '#9CA3AF', // gray-400
    inverse: '#1F2937', // gray-800
    accent: '#818CF8', // indigo-400
  },

  // Input colors
  input: {
    background: '#1F2937', // slate-900
    border: '#374151', // gray-700
    placeholder: '#9CA3AF', // gray-400
    text: '#F9FAFB', // gray-50
  },

  // Border colors
  border: '#374151', // gray-700

  // Status colors
  success: '#34D399', // emerald-400
  warning: '#FBBF24', // amber-400
  danger: '#F87171', // red-400
  info: '#60A5FA', // blue-400

  // Component specific
  statusBar: 'light-content',
  tabBar: '#1F2937', // slate-900
  tabBarInactive: '#9CA3AF', // gray-400
  tabBarActive: '#818CF8', // indigo-400
  shadow: 'rgba(0, 0, 0, 0.3)',

  // Gradients
  gradients: {
    primary: ['#6366F1', '#818CF8'], // indigo-500 to indigo-400
    secondary: ['#34D399', '#6EE7B7'], // emerald-400 to emerald-300
  },
};

export const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const deviceTheme = useColorScheme();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [theme, setTheme] = useState(lightTheme); // Initialize with lightTheme

  // Toggle between dark and light mode
  const toggleTheme = async () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    setTheme(newMode ? darkTheme : lightTheme);
    try {
      await AsyncStorage.setItem('isDarkMode', JSON.stringify(newMode));
    } catch (error) {
      console.log('Error saving theme preference:', error);
    }
  };

  // Load saved theme preference
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem('isDarkMode');
        let newDarkMode;

        if (savedTheme !== null) {
          newDarkMode = JSON.parse(savedTheme);
        } else {
          // If no saved preference, use device theme
          newDarkMode = deviceTheme === 'dark';
        }

        setIsDarkMode(newDarkMode);
        setTheme(newDarkMode ? darkTheme : lightTheme);
      } catch (error) {
        console.log('Error loading theme preference:', error);
        // Default to device theme if there's an error
        const fallbackDarkMode = deviceTheme === 'dark';
        setIsDarkMode(fallbackDarkMode);
        setTheme(fallbackDarkMode ? darkTheme : lightTheme);
      } finally {
        setIsLoading(false);
      }
    };

    loadThemePreference();
  }, [deviceTheme]);

  // Provide a loading placeholder or the actual content
  if (isLoading) {
    return (
      <ThemeContext.Provider value={{ theme: lightTheme, isDarkMode: false, toggleTheme, isLoading: true }}>
        {children}
      </ThemeContext.Provider>
    );
  }

  return (
    <ThemeContext.Provider value={{ theme, isDarkMode, toggleTheme, isLoading: false }}>
      {children}
    </ThemeContext.Provider>
  );
};
