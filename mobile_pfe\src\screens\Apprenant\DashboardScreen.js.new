import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Image
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { AuthContext } from '../../contexts/AuthContext';
import coursService from '../../services/coursService';
import LoadingScreen from '../../components/LoadingScreen';
import { mockDashboardStats } from '../../data/mockData';

const StatCard = ({ title, value, icon, color }) => {
  return (
    <View style={styles.statCard}>
      <View style={[styles.iconContainer, { backgroundColor: color + '20' }]}>
        <MaterialCommunityIcons name={icon} size={24} color={color} />
      </View>
      <Text style={styles.statValue}>{value}</Text>
      <Text style={styles.statTitle}>{title}</Text>
    </View>
  );
};

const ProgressItem = ({ title, progress, color }) => {
  return (
    <View style={styles.progressItem}>
      <View style={styles.progressHeader}>
        <Text style={styles.progressTitle} numberOfLines={1}>{title}</Text>
        <Text style={styles.progressPercentage}>{progress}%</Text>
      </View>
      <View style={styles.progressBar}>
        <View
          style={[
            styles.progressFill,
            { width: `${progress}%`, backgroundColor: color }
          ]}
        />
      </View>
    </View>
  );
};

const ActivityItem = ({ title, time, icon, status }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'completed': return '#10B981';
      case 'in-progress': return '#F59E0B';
      case 'pending': return '#6B7280';
      case 'new': return '#4F46E5';
      default: return '#6B7280';
    }
  };

  return (
    <View style={styles.activityItem}>
      <View style={[styles.activityIcon, { backgroundColor: getStatusColor() + '20' }]}>
        <MaterialCommunityIcons name={icon} size={20} color={getStatusColor()} />
      </View>
      <View style={styles.activityContent}>
        <Text style={styles.activityTitle}>{title}</Text>
        <Text style={styles.activityTime}>{time}</Text>
      </View>
    </View>
  );
};

const DashboardScreen = () => {
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);

  const [stats, setStats] = useState({
    coursesInProgress: 0,
    completedQuizzes: '0/0',
    certifications: 0,
    overallProgress: 0,
  });

  const [courseProgress, setCourseProgress] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);

  const fetchDashboardData = async () => {
    try {
      setError(null);
      console.log('Using mock dashboard data');
      
      // Use mock data instead of API calls
      setStats({
        coursesInProgress: mockDashboardStats.coursesInProgress,
        completedQuizzes: mockDashboardStats.completedQuizzes,
        certifications: mockDashboardStats.certifications,
        overallProgress: mockDashboardStats.overallProgress,
      });
      
      setCourseProgress(mockDashboardStats.courseProgress);
      setRecentActivities(mockDashboardStats.recentActivities);
      
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Impossible de charger les données du tableau de bord');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchDashboardData();
      setLoading(false);
    };

    loadData();
  }, []);

  const getRandomColor = () => {
    const colors = ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  if (loading) {
    return <LoadingScreen message="Chargement du tableau de bord..." />;
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>Bonjour,</Text>
          <Text style={styles.userName}>{user?.name || 'Apprenant'}</Text>
        </View>
        <Image
          source={
            user?.profileImage
              ? { uri: user.profileImage }
              : require('../../../assets/default-avatar.png')
          }
          style={styles.avatar}
        />
      </View>

      {error ? (
        <View style={styles.errorContainer}>
          <MaterialCommunityIcons name="alert-circle" size={48} color="#EF4444" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={onRefresh}>
            <Text style={styles.retryButtonText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          {/* Stats Cards */}
          <View style={styles.statsContainer}>
            <StatCard
              title="Cours en cours"
              value={stats.coursesInProgress}
              icon="book-open-variant"
              color="#4F46E5"
            />
            <StatCard
              title="Quiz complétés"
              value={stats.completedQuizzes}
              icon="file-document-outline"
              color="#10B981"
            />
            <StatCard
              title="Certifications"
              value={stats.certifications}
              icon="certificate"
              color="#F59E0B"
            />
            <StatCard
              title="Progression"
              value={`${stats.overallProgress}%`}
              icon="chart-line"
              color="#8B5CF6"
            />
          </View>

          {/* Course Progress */}
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Progression des cours</Text>
            {courseProgress.length > 0 ? (
              <View style={styles.progressContainer}>
                {courseProgress.map((course, index) => (
                  <ProgressItem
                    key={index}
                    title={course.title}
                    progress={course.progress}
                    color={course.color}
                  />
                ))}
              </View>
            ) : (
              <View style={styles.emptyStateContainer}>
                <MaterialCommunityIcons name="book-open-variant" size={48} color="#6B7280" />
                <Text style={styles.emptyStateText}>
                  Aucun cours en progression
                </Text>
                <Text style={styles.emptyStateSubtext}>
                  Commencez à suivre des cours pour voir votre progression ici
                </Text>
              </View>
            )}
          </View>

          {/* Recent Activities */}
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Activités récentes</Text>
            <View style={styles.activitiesContainer}>
              {recentActivities.map((activity, index) => (
                <ActivityItem
                  key={index}
                  title={activity.title}
                  time={activity.time}
                  icon={activity.icon}
                  status={activity.status}
                />
              ))}
            </View>
          </View>
        </>
      )}
    </ScrollView>
  );
};
