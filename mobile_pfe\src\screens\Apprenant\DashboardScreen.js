import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { AuthContext } from '../../contexts/AuthContext';
import { ThemeContext } from '../../contexts/ThemeContext';
import coursService from '../../services/coursService';
import LoadingScreen from '../../components/LoadingScreen';
import dashboardService from '../../services/dashboardService';
import AppLayout from '../../components/AppLayout';
import AppCard from '../../components/AppCard';
import AppButton from '../../components/AppButton';

const StatCard = ({ title, value, icon, color }) => {
  const { theme } = useContext(ThemeContext);

  return (
    <AppCard
      style={styles.statCard}
      contentStyle={styles.statCardContent}
      showBorder={false}
    >
      <View style={[styles.iconContainer, { backgroundColor: color + '20' }]}>
        <MaterialCommunityIcons name={icon} size={24} color={color} />
      </View>
      <Text style={[styles.statValue, { color: theme.text.primary }]}>{value}</Text>
      <Text style={[styles.statTitle, { color: theme.text.secondary }]}>{title}</Text>
    </AppCard>
  );
};

const ProgressItem = ({ title, progress, color }) => {
  const { theme } = useContext(ThemeContext);

  return (
    <View style={styles.progressItem}>
      <View style={styles.progressHeader}>
        <Text style={[styles.progressTitle, { color: theme.text.primary }]} numberOfLines={1}>{title}</Text>
        <Text style={[styles.progressPercentage, { color: theme.primary }]}>{progress}%</Text>
      </View>
      <View style={[styles.progressBar, { backgroundColor: theme.border }]}>
        <View
          style={[
            styles.progressFill,
            { width: `${progress}%`, backgroundColor: color }
          ]}
        />
      </View>
    </View>
  );
};

const ActivityItem = ({ title, time, icon, status }) => {
  const { theme } = useContext(ThemeContext);

  const getStatusColor = () => {
    switch (status) {
      case 'completed': return theme.success;
      case 'in-progress': return theme.warning;
      case 'pending': return theme.text.tertiary;
      default: return theme.text.tertiary;
    }
  };

  return (
    <View style={styles.activityItem}>
      <View style={[styles.activityIcon, { backgroundColor: getStatusColor() + '20' }]}>
        <MaterialCommunityIcons name={icon} size={20} color={getStatusColor()} />
      </View>
      <View style={styles.activityContent}>
        <Text style={[styles.activityTitle, { color: theme.text.primary }]}>{title}</Text>
        <Text style={[styles.activityTime, { color: theme.text.secondary }]}>{time}</Text>
      </View>
    </View>
  );
};

const DashboardScreen = ({ navigation }) => {
  const { user } = useContext(AuthContext);
  const { theme, isDarkMode } = useContext(ThemeContext);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);

  const [stats, setStats] = useState({
    coursesInProgress: 0,
    completedQuizzes: '0/0',
    certifications: 0,
    overallProgress: 0,
  });

  const [courseProgress, setCourseProgress] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);

  const fetchDashboardData = async () => {
    try {
      setError(null);
      console.log('Fetching dashboard data from real API');

      // Get all dashboard data from real API
      const dashboardData = await dashboardService.getAllDashboardData();

      // Update state with real data
      if (dashboardData.stats) {
        setStats({
          coursesInProgress: dashboardData.stats.coursesInProgress || 0,
          completedQuizzes: dashboardData.stats.completedQuizzes || 0,
          certifications: dashboardData.stats.certifications || 0,
          overallProgress: dashboardData.stats.overallProgress || 0,
        });
      }

      setCourseProgress(dashboardData.courseProgress || []);
      setRecentActivities(dashboardData.activities || []);

    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Impossible de charger les données du tableau de bord');

      // Fallback to empty data on error
      setStats({
        coursesInProgress: 0,
        completedQuizzes: 0,
        certifications: 0,
        overallProgress: 0,
      });
      setCourseProgress([]);
      setRecentActivities([]);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchDashboardData();
      setLoading(false);
    };

    loadData();
  }, []);

  const getRandomColor = () => {
    const colors = ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  if (loading) {
    return <LoadingScreen message="Chargement du tableau de bord..." />;
  }

  const renderDashboardContent = () => {
    if (error) {
      return (
        <View style={styles.errorContainer}>
          <MaterialCommunityIcons name="alert-circle" size={48} color={theme.danger} />
          <Text style={[styles.errorText, { color: theme.text.primary }]}>{error}</Text>
          <AppButton
            title="Réessayer"
            onPress={onRefresh}
            variant="primary"
            size="medium"
          />
        </View>
      );
    }

    return (
      <>
        {/* Header with welcome message */}
        <View style={styles.header}>
          <View>
            <Text style={[styles.greeting, { color: theme.text.secondary }]}>Bonjour,</Text>
            <Text style={[styles.userName, { color: theme.text.primary }]}>{user?.name || 'Apprenant'}</Text>
          </View>
          <View style={[styles.dateContainer, { backgroundColor: isDarkMode ? theme.card : '#F3F4F6' }]}>
            <MaterialCommunityIcons name="calendar" size={16} color={theme.primary} style={styles.dateIcon} />
            <Text style={[styles.dateText, { color: theme.text.secondary }]}>
              {new Date().toLocaleDateString('fr-FR', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
            </Text>
          </View>
        </View>

        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <StatCard
            title="Cours en cours"
            value={stats.coursesInProgress}
            icon="book-open-variant"
            color={theme.primary}
          />
          <StatCard
            title="Quiz complétés"
            value={stats.completedQuizzes}
            icon="file-document-outline"
            color={theme.secondary}
          />
          <StatCard
            title="Certifications"
            value={stats.certifications}
            icon="certificate"
            color={theme.accent}
          />
          <StatCard
            title="Progression"
            value={`${stats.overallProgress}%`}
            icon="chart-line"
            color={isDarkMode ? '#8B5CF6' : '#4F46E5'}
          />
        </View>

        {/* Course Progress */}
        <AppCard
          title="Progression des cours"
          style={styles.sectionContainer}
          icon="chart-bar"
          iconColor={theme.primary}
        >
          {courseProgress.length > 0 ? (
            <View style={styles.progressContainer}>
              {courseProgress.map((course, index) => (
                <ProgressItem
                  key={index}
                  title={course.title}
                  progress={course.progress}
                  color={course.color}
                />
              ))}
            </View>
          ) : (
            <View style={styles.emptyStateContainer}>
              <MaterialCommunityIcons name="book-open-variant" size={48} color={theme.text.tertiary} />
              <Text style={[styles.emptyStateText, { color: theme.text.primary }]}>
                Aucun cours en progression
              </Text>
              <Text style={[styles.emptyStateSubtext, { color: theme.text.secondary }]}>
                Commencez à suivre des cours pour voir votre progression ici
              </Text>
              <TouchableOpacity
                style={[styles.exploreButton, { backgroundColor: theme.primary }]}
                onPress={() => navigation.navigate('Courses')}
              >
                <MaterialCommunityIcons name="book-open-variant" size={16} color="#FFFFFF" />
                <Text style={styles.exploreButtonText}>Explorer les cours</Text>
              </TouchableOpacity>
            </View>
          )}
        </AppCard>

        {/* Recent Activities */}
        <AppCard
          title="Activités récentes"
          style={styles.sectionContainer}
          icon="clock"
          iconColor={isDarkMode ? '#8B5CF6' : '#4F46E5'}
        >
          <View style={styles.activitiesContainer}>
            {recentActivities.length > 0 ? (
              recentActivities.map((activity, index) => (
                <ActivityItem
                  key={index}
                  title={activity.title}
                  time={activity.time}
                  icon={activity.icon}
                  status={activity.status}
                />
              ))
            ) : (
              <View style={styles.emptyStateContainer}>
                <MaterialCommunityIcons name="history" size={48} color={theme.text.tertiary} />
                <Text style={[styles.emptyStateText, { color: theme.text.primary }]}>
                  Aucune activité récente
                </Text>
                <Text style={[styles.emptyStateSubtext, { color: theme.text.secondary }]}>
                  Vos activités apparaîtront ici au fur et à mesure de votre progression
                </Text>
              </View>
            )}
          </View>
        </AppCard>
      </>
    );
  };

  return (
    <AppLayout
      title="Tableau de bord"
      scrollable={true}
      refreshing={refreshing}
      onRefresh={onRefresh}
      showProfileButton={true}
    >
      {renderDashboardContent()}
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  header: {
    marginBottom: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  greeting: {
    fontSize: 16,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
  },
  dateIcon: {
    marginRight: 6,
  },
  dateText: {
    fontSize: 12,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    width: '48%',
    marginBottom: 16,
  },
  statCardContent: {
    padding: 16,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 14,
  },
  sectionContainer: {
    marginBottom: 16,
  },
  progressContainer: {
    gap: 12,
  },
  progressItem: {
    marginBottom: 12,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressTitle: {
    fontSize: 14,
    flex: 1,
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  activitiesContainer: {
    gap: 12,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    marginBottom: 4,
  },
  activityTime: {
    fontSize: 12,
  },
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  exploreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 8,
  },
  exploreButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
    marginLeft: 8,
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    marginBottom: 24,
  },
  errorText: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
});

export default DashboardScreen;