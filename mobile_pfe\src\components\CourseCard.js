import React, { useContext } from "react";
import { View, Text, StyleSheet, Image, TouchableOpacity } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { ThemeContext } from "../contexts/ThemeContext";
import AppCard from "./AppCard";

const CourseCard = ({ course, onPress, onCertificatePress }) => {
  const { theme, isDarkMode } = useContext(ThemeContext);

  // Calculate progress percentage using the new progression data
  const progressPercentage = course.progress_percentage || course.progress || 0;

  // Get status based on progress
  const getStatus = () => {
    if (!progressPercentage || progressPercentage === 0) {
      return {
        label: "Non commencé",
        color: isDarkMode ? "#6B7280" : "#9CA3AF",
        bgColor: isDarkMode ? "#3F3F46" : "#F3F4F6",
      };
    } else if (progressPercentage === 100 || course.is_completed) {
      return {
        label: "Terminé",
        color: "#10B981",
        bgColor: isDarkMode ? "#064E3B" : "#D1FAE5",
      };
    } else {
      return {
        label: "En cours",
        color: "#3B82F6",
        bgColor: isDarkMode ? "#1E3A8A" : "#DBEAFE",
      };
    }
  };

  const status = getStatus();

  return (
    <AppCard
      style={styles.card}
      contentStyle={styles.content}
      onPress={() => onPress(course)}
    >
      <View style={styles.cardHeader}>
        {course.category && (
          <View
            style={[
              styles.categoryContainer,
              {
                backgroundColor: isDarkMode ? `${theme.primary}30` : "#EEF2FF",
              },
            ]}
          >
            <Text style={[styles.category, { color: theme.primary }]}>
              {course.category}
            </Text>
          </View>
        )}

        <View
          style={[styles.statusContainer, { backgroundColor: status.bgColor }]}
        >
          <Text style={[styles.statusText, { color: status.color }]}>
            {status.label}
          </Text>
        </View>
      </View>

      <Text
        style={[styles.title, { color: theme.text.primary }]}
        numberOfLines={2}
      >
        {course.titre || course.title}
      </Text>

      <View style={styles.progressContainer}>
        <View style={[styles.progressBar, { backgroundColor: theme.border }]}>
          <View
            style={[
              styles.progressFill,
              { width: progressPercentage, backgroundColor: theme.primary },
            ]}
          />
        </View>
        <Text style={[styles.progressText, { color: theme.primary }]}>
          {progressPercentage}
        </Text>
      </View>

      <View style={styles.footer}>
        <View style={styles.footerItem}>
          <MaterialCommunityIcons
            name="book-open-variant"
            size={16}
            color={theme.text.tertiary}
          />
          <Text style={[styles.footerText, { color: theme.text.tertiary }]}>
            {course.modules ? `${course.modules.length} modules` : "Modules"}
          </Text>
        </View>

        {course.duration && (
          <View style={styles.footerItem}>
            <MaterialCommunityIcons
              name="clock-outline"
              size={16}
              color={theme.text.tertiary}
            />
            <Text style={[styles.footerText, { color: theme.text.tertiary }]}>
              {course.duration}
            </Text>
          </View>
        )}
      </View>
    </AppCard>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    overflow: "hidden",
  },
  content: {
    padding: 16,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  categoryContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: "flex-start",
  },
  category: {
    fontSize: 12,
    fontWeight: "500",
  },
  statusContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "500",
  },
  title: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 12,
  },
  progressContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  progressBar: {
    flex: 1,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: "600",
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  footerItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  footerText: {
    marginLeft: 4,
    fontSize: 12,
  },
  headerRight: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  certificateBadge: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: "center",
    justifyContent: "center",
  },
  quizStats: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
    gap: 4,
  },
});

export default CourseCard;
