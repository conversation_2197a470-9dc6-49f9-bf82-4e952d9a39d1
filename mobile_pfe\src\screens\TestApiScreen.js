import React, { useState, useContext, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Switch,
  Platform
} from 'react-native';
import { ThemeContext } from '../contexts/ThemeContext';
import authService from '../services/authService';
import api, { API_URL } from '../services/api';
import NetInfo from '@react-native-community/netinfo';

const TestApiScreen = ({ navigation }) => {
  const { theme } = useContext(ThemeContext);
  const [apiUrl, setApiUrl] = useState(api.defaults.baseURL);
  const [testResult, setTestResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [networkInfo, setNetworkInfo] = useState(null);

  // Get network information when the component mounts
  useEffect(() => {
    const getNetworkInfo = async () => {
      try {
        const netInfo = await NetInfo.fetch();
        setNetworkInfo(netInfo);
        console.log('Network info:', netInfo);
      } catch (error) {
        console.error('Error getting network info:', error);
      }
    };

    getNetworkInfo();
  }, []);

  const testConnection = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      console.log('Testing real connection to:', apiUrl);

      // Test real API connection using authService
      const result = await authService.testConnection();

      console.log('Real connection test result:', result);
      setTestResult(result);

      if (result.success) {
        Alert.alert(
          'Connexion réussie',
          `Connexion au serveur établie avec succès!\n\nEndpoint: ${result.endpoint}\nAPI URL: ${result.apiUrl}`
        );
      } else {
        Alert.alert(
          'Échec de la connexion',
          `Impossible de se connecter au serveur.\n\nErreur: ${result.message}`
        );
      }
    } catch (error) {
      console.error('Exception in testConnection:', error);
      setTestResult({
        success: false,
        error: error.message || 'An unexpected error occurred',
        details: error,
        mockMode: true
      });
      Alert.alert(
        'Error',
        `Exception: ${error.message}\n\nThis is a client-side error in mock mode.`,
        [
          { text: 'OK' }
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <Text style={[styles.title, { color: theme.text.primary }]}>API Connection Test</Text>

      <View style={styles.inputContainer}>
        <Text style={[styles.label, { color: theme.text.secondary }]}>API URL:</Text>
        <TextInput
          style={[styles.input, {
            backgroundColor: theme.card,
            borderColor: theme.border,
            color: theme.text.primary
          }]}
          value={apiUrl}
          onChangeText={setApiUrl}
          placeholder="Enter API URL"
          placeholderTextColor={theme.text.tertiary}
        />
        <View style={styles.quickUrlsContainer}>
          <TouchableOpacity
            style={[styles.quickUrlButton, { backgroundColor: theme.primary + '30' }]}
            onPress={() => setApiUrl('http://********:8000/api')}
          >
            <Text style={[styles.quickUrlText, { color: theme.primary }]}>Android</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.quickUrlButton, { backgroundColor: theme.primary + '30' }]}
            onPress={() => setApiUrl('http://localhost:8000/api')}
          >
            <Text style={[styles.quickUrlText, { color: theme.primary }]}>iOS Local</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.quickUrlButton, { backgroundColor: theme.success + '30' }]}
            onPress={() => setApiUrl('http://*************:8000/api')}
          >
            <Text style={[styles.quickUrlText, { color: theme.success }]}>IP Address</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.quickUrlsContainer}>
          <TouchableOpacity
            style={[styles.quickUrlButton, { backgroundColor: theme.primary + '30' }]}
            onPress={() => setApiUrl('http://********:8000')}
          >
            <Text style={[styles.quickUrlText, { color: theme.primary }]}>No /api</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.quickUrlButton, { backgroundColor: theme.primary + '30' }]}
            onPress={() => setApiUrl('http://*************:8000')}
          >
            <Text style={[styles.quickUrlText, { color: theme.primary }]}>IP No /api</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.quickUrlButton, { backgroundColor: theme.primary + '30' }]}
            onPress={() => setApiUrl('http://*************:3000/api')}
          >
            <Text style={[styles.quickUrlText, { color: theme.primary }]}>Port 3000</Text>
          </TouchableOpacity>
        </View>
      </View>

      <Text style={[styles.mockApiDescription, { color: theme.text.tertiary }]}>
        Backend connections are disabled in this version. This will only simulate a connection test.
      </Text>

      {networkInfo && (
        <View style={styles.networkInfoContainer}>
          <Text style={[styles.networkInfoTitle, { color: theme.text.secondary }]}>
            Network Information:
          </Text>
          <Text style={[styles.networkInfoText, { color: theme.text.tertiary }]}>
            Type: {networkInfo.type} {networkInfo.isConnected ? '(Connected)' : '(Disconnected)'}
          </Text>
          {networkInfo.details && networkInfo.details.ipAddress && (
            <Text style={[styles.networkInfoText, { color: theme.text.tertiary }]}>
              Device IP: {networkInfo.details.ipAddress}
            </Text>
          )}
          <Text style={[styles.networkInfoText, { color: theme.text.tertiary }]}>
            Backend URL: {apiUrl}
          </Text>
          <Text style={[styles.networkInfoHint, { color: theme.text.tertiary }]}>
            Hint: Make sure your backend is running and accessible from this device.
            {Platform.OS === 'ios' ? " On iOS, 'localhost' refers to the device itself, not your computer." : ''}
          </Text>
        </View>
      )}

      <TouchableOpacity
        style={[
          styles.button,
          { backgroundColor: theme.primary },
          isLoading && styles.buttonDisabled
        ]}
        onPress={testConnection}
        disabled={isLoading}
      >
        <Text style={styles.buttonText}>
          {isLoading ? 'Testing...' : 'Test Connection'}
        </Text>
      </TouchableOpacity>

      {testResult && (
        <ScrollView
          style={[styles.resultContainer, {
            backgroundColor: theme.card,
            borderColor: theme.border
          }]}
        >
          <Text style={[
            styles.resultTitle,
            {
              color: testResult.success ? theme.success : theme.danger
            }
          ]}>
            Test Result: {testResult.success ? 'SUCCESS' : 'FAILED'}
          </Text>

          {testResult.error && (
            <Text style={[styles.errorText, { color: theme.danger }]}>
              Error: {testResult.error}
            </Text>
          )}

          {testResult.apiUrl && (
            <Text style={[styles.infoText, { color: theme.text.secondary }]}>
              API URL: {testResult.apiUrl}
            </Text>
          )}

          {testResult.endpoint && (
            <Text style={[styles.infoText, { color: theme.text.secondary }]}>
              Endpoint: {testResult.endpoint}
            </Text>
          )}

          {testResult.data && (
            <Text style={[styles.dataText, { color: theme.success }]}>
              Response: {JSON.stringify(testResult.data, null, 2)}
            </Text>
          )}

          {testResult.allErrors && testResult.allErrors.length > 0 && (
            <>
              <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>
                Attempted Endpoints:
              </Text>
              {testResult.allErrors.map((err, index) => (
                <View key={index} style={styles.errorItem}>
                  <Text style={[styles.errorEndpoint, { color: theme.text.secondary }]}>
                    {err.endpoint}:
                  </Text>
                  <Text style={[styles.errorMessage, { color: theme.danger }]}>
                    {err.message} {err.code ? `(${err.code})` : ''}
                  </Text>
                  {err.status && (
                    <Text style={[styles.errorStatus, { color: theme.text.tertiary }]}>
                      Status: {err.status} {err.statusText}
                    </Text>
                  )}
                </View>
              ))}
            </>
          )}

          {testResult.details && (
            <Text style={[styles.detailsText, { color: theme.text.tertiary }]}>
              Details: {JSON.stringify(testResult.details, null, 2)}
            </Text>
          )}
        </ScrollView>
      )}

      <TouchableOpacity
        style={[styles.button, { backgroundColor: theme.text.secondary }]}
        onPress={() => navigation.goBack()}
      >
        <Text style={styles.buttonText}>Back</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  quickUrlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  quickUrlButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    marginRight: 8,
  },
  quickUrlText: {
    fontSize: 12,
    fontWeight: '600',
  },

  mockApiDescription: {
    fontSize: 14,
    marginBottom: 16,
    fontStyle: 'italic',
  },
  button: {
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  resultContainer: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    maxHeight: 300,
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  errorText: {
    marginBottom: 8,
  },
  dataText: {
    marginBottom: 8,
  },
  infoText: {
    marginBottom: 8,
  },
  detailsText: {
    fontSize: 12,
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
  },
  errorItem: {
    marginBottom: 10,
    paddingLeft: 8,
    borderLeftWidth: 2,
  },
  errorEndpoint: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  errorMessage: {
    fontSize: 12,
    marginBottom: 2,
  },
  errorStatus: {
    fontSize: 11,
    fontStyle: 'italic',
  },
  networkInfoContainer: {
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  networkInfoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  networkInfoText: {
    fontSize: 12,
    marginBottom: 2,
  },
  networkInfoHint: {
    fontSize: 11,
    fontStyle: 'italic',
    marginTop: 6,
  },
});

export default TestApiScreen;
