# Tests de Synchronisation Mobile-Web

## Fonctionnalités Synchronisées

### ✅ 1. Récupération des Cours
- **Web** : `/apprenant/cours` via QuizService.getApprenantCours()
- **Mobile** : `/apprenant/cours` via coursService.getApprenantCours()
- **Test** : Vérifier que les mêmes cours apparaissent sur web et mobile

### ✅ 2. Progression des Cours
- **Web** : `/progression/apprenant/{id}/cours/{courseId}` 
- **Mobile** : `/progression/apprenant/{id}/cours/{courseId}`
- **Test** : Vérifier que le pourcentage de progression est identique

### ✅ 3. Quiz et Questions
- **Web** : `/quiz?cours={courseId}` et `/quiz/{IDModule}`
- **Mobile** : `/quiz?cours={courseId}` et `/quiz/{IDModule}`
- **Test** : Vérifier que les mêmes quiz sont disponibles

### ✅ 4. Soumission des Quiz
- **Web** : Évaluation via formateur
- **Mobile** : `/quiz/{quizId}/submit` avec fallback vers données mock
- **Test** : Vérifier que les réponses sont enregistrées

### ✅ 5. Évaluations
- **Web** : `/evaluation/quiz/{quizId}/apprenant/{apprenantId}`
- **Mobile** : `/evaluation/quiz/{quizId}/apprenant/{apprenantId}`
- **Test** : Vérifier que les évaluations sont synchronisées

### ✅ 6. Certificats
- **Web** : `/certificat/generate-direct` et `/certificat/apprenant/{id}`
- **Mobile** : `/certificat/generate-direct` et `/certificat/apprenant/{id}`
- **Test** : Vérifier la génération et l'affichage des certificats

## Scénarios de Test

### Scénario 1 : Connexion et Cours
1. Se connecter en tant qu'apprenant sur mobile
2. Vérifier que les cours affichés correspondent au web
3. Vérifier que la progression est identique

### Scénario 2 : Passage de Quiz
1. Sélectionner un cours avec quiz
2. Lancer un quiz depuis le mobile
3. Répondre aux questions
4. Vérifier que l'évaluation est enregistrée
5. Comparer avec l'interface web

### Scénario 3 : Progression et Certificats
1. Compléter tous les quiz d'un cours
2. Vérifier que la progression atteint 100%
3. Générer le certificat depuis mobile
4. Vérifier que le certificat apparaît sur web

## Points d'Attention

### Gestion des Erreurs
- ✅ Fallback vers données mock si API indisponible
- ✅ Messages d'erreur informatifs
- ✅ Retry automatique pour certaines opérations

### Synchronisation des Données
- ✅ Même format de données entre web et mobile
- ✅ Même endpoints API utilisés
- ✅ Même logique de progression

### Interface Utilisateur
- ✅ Affichage cohérent de la progression
- ✅ Badges de certificats
- ✅ Statistiques de quiz identiques

## Commandes de Test

```bash
# Démarrer le backend
cd backend_pfe
php -S 127.0.0.1:8000 -t public

# Démarrer le frontend web
cd frontend_pfe
npm run dev

# Démarrer l'app mobile
cd mobile_pfe
npm start
```

## Vérifications Finales

- [ ] Les cours s'affichent identiquement
- [ ] La progression est synchronisée
- [ ] Les quiz fonctionnent sur mobile
- [ ] Les évaluations sont enregistrées
- [ ] Les certificats sont générés
- [ ] L'interface est cohérente

## Notes Techniques

### APIs Utilisées
- `/apprenant/cours` - Liste des cours
- `/quiz?cours={id}` - Quiz d'un cours
- `/quiz/{IDModule}` - Détails d'un quiz
- `/progression/apprenant/{id}/cours/{courseId}` - Progression
- `/evaluation/*` - Évaluations
- `/certificat/*` - Certificats

### Données Synchronisées
- Titre et description des cours
- Progression en pourcentage
- Statut des quiz (réussi/échoué)
- Certificats obtenus
- Statistiques de completion
