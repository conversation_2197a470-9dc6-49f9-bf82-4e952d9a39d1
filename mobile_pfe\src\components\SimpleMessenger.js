import React, { useState, useContext, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Image
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { ThemeContext } from '../contexts/ThemeContext';

/**
 * Composant de messagerie simple style Facebook Messenger
 */
const SimpleMessenger = ({ conversation, onBack }) => {
  const { theme, isDarkMode } = useContext(ThemeContext);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: 'Bonjour, comment allez-vous?',
      sender: 'them',
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString()
    },
    {
      id: 2,
      text: 'Je vais bien, merci! Et vous?',
      sender: 'me',
      timestamp: new Date(Date.now() - 1000 * 60 * 25).toISOString()
    },
    {
      id: 3,
      text: 'Très bien, merci. Avez-vous terminé le projet?',
      sender: 'them',
      timestamp: new Date(Date.now() - 1000 * 60 * 20).toISOString()
    },
    {
      id: 4,
      text: 'Oui, je viens de le terminer. Je vous enverrai les détails demain.',
      sender: 'me',
      timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString()
    },
    {
      id: 5,
      text: 'Parfait! J\'ai hâte de voir ça.',
      sender: 'them',
      timestamp: new Date(Date.now() - 1000 * 60 * 10).toISOString()
    }
  ]);
  const [sending, setSending] = useState(false);
  const flatListRef = useRef(null);
  
  // Faire défiler vers le bas lorsque de nouveaux messages sont ajoutés
  useEffect(() => {
    if (flatListRef.current && messages.length > 0) {
      setTimeout(() => {
        flatListRef.current.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);
  
  // Envoyer un message
  const handleSendMessage = () => {
    if (!message.trim()) return;
    
    setSending(true);
    
    // Simuler l'envoi d'un message
    setTimeout(() => {
      const newMessage = {
        id: Date.now(),
        text: message.trim(),
        sender: 'me',
        timestamp: new Date().toISOString()
      };
      
      setMessages([...messages, newMessage]);
      setMessage('');
      setSending(false);
      
      // Simuler une réponse après 2 secondes
      setTimeout(() => {
        const responseMessage = {
          id: Date.now() + 1,
          text: 'D\'accord, merci pour l\'information!',
          sender: 'them',
          timestamp: new Date().toISOString()
        };
        
        setMessages(prevMessages => [...prevMessages, responseMessage]);
      }, 2000);
    }, 500);
  };
  
  // Formater l'heure du message
  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  // Rendu d'un message
  const renderMessage = ({ item, index }) => {
    const isMe = item.sender === 'me';
    const showAvatar = !isMe && (index === 0 || messages[index - 1].sender !== item.sender);
    
    return (
      <View style={[
        styles.messageContainer,
        isMe ? styles.myMessageContainer : styles.theirMessageContainer
      ]}>
        {!isMe && showAvatar && (
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {conversation?.user?.name?.charAt(0) || 'U'}
            </Text>
          </View>
        )}
        
        <View style={[
          styles.messageBubble,
          isMe ? styles.myMessage : styles.theirMessage,
          { backgroundColor: isMe ? '#0084FF' : isDarkMode ? '#333333' : '#E4E6EB' }
        ]}>
          <Text style={[
            styles.messageText,
            { color: isMe ? 'white' : isDarkMode ? 'white' : 'black' }
          ]}>
            {item.text}
          </Text>
        </View>
      </View>
    );
  };
  
  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: isDarkMode ? 'black' : 'white' }]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      {/* En-tête */}
      <View style={[styles.header, { borderBottomColor: isDarkMode ? '#333333' : '#E4E6EB' }]}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <MaterialCommunityIcons 
            name="chevron-left" 
            size={30} 
            color={isDarkMode ? 'white' : '#0084FF'} 
          />
        </TouchableOpacity>
        
        <View style={styles.headerInfo}>
          <Text style={[styles.headerName, { color: isDarkMode ? 'white' : 'black' }]}>
            {conversation?.user?.name || 'Utilisateur'}
          </Text>
          <Text style={[styles.headerStatus, { color: isDarkMode ? '#CCCCCC' : '#65676B' }]}>
            Active now
          </Text>
        </View>
      </View>
      
      {/* Liste des messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        keyExtractor={item => item.id.toString()}
        renderItem={renderMessage}
        contentContainerStyle={styles.messagesList}
      />
      
      {/* Zone de saisie */}
      <View style={styles.inputContainer}>
        <View style={[
          styles.inputWrapper, 
          { backgroundColor: isDarkMode ? '#333333' : '#F0F2F5' }
        ]}>
          <TextInput
            style={[styles.input, { color: isDarkMode ? 'white' : 'black' }]}
            placeholder="Aa"
            placeholderTextColor={isDarkMode ? '#AAAAAA' : '#65676B'}
            value={message}
            onChangeText={setMessage}
            multiline
          />
          
          <TouchableOpacity style={styles.emojiButton}>
            <MaterialCommunityIcons 
              name="emoticon-outline" 
              size={24} 
              color={isDarkMode ? 'white' : '#0084FF'} 
            />
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity 
          style={styles.sendButton}
          onPress={handleSendMessage}
          disabled={!message.trim() || sending}
        >
          {sending ? (
            <ActivityIndicator size="small" color={isDarkMode ? 'white' : '#0084FF'} />
          ) : (
            <MaterialCommunityIcons 
              name={message.trim() ? "send" : "thumb-up"} 
              size={24} 
              color={isDarkMode ? 'white' : '#0084FF'} 
            />
          )}
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 5,
  },
  headerInfo: {
    marginLeft: 10,
  },
  headerName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  headerStatus: {
    fontSize: 12,
  },
  messagesList: {
    padding: 10,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'flex-end',
  },
  myMessageContainer: {
    justifyContent: 'flex-end',
  },
  theirMessageContainer: {
    justifyContent: 'flex-start',
  },
  avatar: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#0084FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 5,
  },
  avatarText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  messageBubble: {
    borderRadius: 18,
    paddingHorizontal: 12,
    paddingVertical: 8,
    maxWidth: '70%',
  },
  myMessage: {
    borderBottomRightRadius: 4,
  },
  theirMessage: {
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontSize: 16,
    maxHeight: 100,
    padding: 0,
  },
  emojiButton: {
    marginLeft: 8,
  },
  sendButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SimpleMessenger;
