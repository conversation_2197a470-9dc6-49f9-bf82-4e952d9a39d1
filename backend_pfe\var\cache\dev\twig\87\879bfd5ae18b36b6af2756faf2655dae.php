<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* @ApiPlatform/DataCollector/api-platform-icon.svg */
class __TwigTemplate_43b6fcc92fef9eafd17d90035603377b extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "@ApiPlatform/DataCollector/api-platform-icon.svg"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "@ApiPlatform/DataCollector/api-platform-icon.svg"));

        // line 1
        yield "<svg width=\"24\" height=\"24\" viewBox=\"0 120 256 300\" xmlns=\"http://www.w3.org/2000/svg\"><g data-name=\"spider\"><g fill=\"#aaa\"><path d=\"M40.66 321.08L23.67 248.1l31.32.56 1.77 7.43-24.21-.5 15.36 63.08-7.25 2.41z\"/><path d=\"M36.81 331s-6.21 11.73 5.36 16.56 17-5.51 17-5.51.89-4.49-4.79-14.32-7.38-11.9-10.56-12.43-7.01 15.7-7.01 15.7z\"/></g><g fill=\"#aaa\"><path d=\"M21.27 383.93L0 246.05l57.07-25.51-.73 7.59-47.79 22.31L28.9 384l-7.63-.07z\"/><path d=\"m14.57 396.19s-9.28 13 3.75 20.59 21.17-3.84 21.17-3.84 1.78-5.21-3.43-17.83-6.91-15.4-10.63-16.54-10.86 17.62-10.86 17.62z\"/></g><g fill=\"#aaa\"><path d=\"M215.59 320.08l16.99-72.98-31.32.56-1.77 7.43 24.21-.5-15.36 63.08 7.25 2.41z\"/><path d=\"M219.44 330s6.21 11.73-5.36 16.56-17-5.51-17-5.51-.89-4.49 4.79-14.32 7.4-11.93 10.59-12.46 6.98 15.73 6.98 15.73z\"/></g><g fill=\"#aaa\"><path d=\"M234.98 382.93l21.27-137.88-57.07-25.51.73 7.59 47.8 22.31L227.36 383l7.62-.07z\"/><path d=\"m241.68 395.19s9.28 13-3.75 20.59-21.17-3.84-21.17-3.84-1.78-5.21 3.43-17.83 6.91-15.4 10.63-16.54 10.86 17.62 10.86 17.62z\"/></g><path d=\"m207.23 219.63c0 41-35.77 62.69-79.9 62.69s-77.67-21.69-77.67-62.69 33.54-71.22 77.67-71.22 79.9 30.22 79.9 71.22z\" fill=\"#d2d2d2\"/><path d=\"m126.33 285.72c-23.33 0-43.33-5.79-57.75-16.72-15.46-11.73-23.63-28.79-23.63-49.34 0-38.35 28.14-74.62 81.38-74.62 53.92 0 83.62 36.82 83.62 74.62 0 20.47-8.58 37.54-24.8 49.38-14.78 10.75-35.66 16.68-58.82 16.68zm0-133.91c-49.07 0-74 33.44-74 67.82 0 36.58 28.34 59.3 74 59.3 46.28 0 76.18-23.28 76.18-59.3 0-42.83-41.42-67.82-76.18-67.82z\" fill=\"#aaa\"/><ellipse cx=\"92.29\" cy=\"165.77\" rx=\"37.55\" ry=\"36.82\" fill=\"#fff\"/><path d=\"M91.29 205c-22 0-39.92-17.59-39.92-39.2s17.91-39.2 39.92-39.2 39.93 17.59 39.93 39.2S113.3 205 91.29 205zm0-73.65c-19.39 0-35.17 15.45-35.17 34.45s15.78 34.45 35.17 34.45 35.17-15.45 35.17-34.45-15.78-34.47-35.17-34.47z\" fill=\"#aaa\"/><ellipse cx=\"95.19\" cy=\"167.95\" rx=\"16.99\" ry=\"16.3\"/><circle cx=\"92.02\" cy=\"165.04\" r=\"4.68\" fill=\"#fff\"/><ellipse cx=\"166.29\" cy=\"171.09\" rx=\"45.51\" ry=\"47.07\" fill=\"#fff\"/><path d=\"m165.29 220.53c-26.41 0-47.89-22.18-47.89-49.44s21.48-49.44 47.89-49.44 47.89 22.18 47.89 49.44-21.48 49.44-47.89 49.44zm0-94.13c-23.78 0-43.14 20-43.14 44.69s19.35 44.69 43.14 44.69 43.13-20 43.13-44.69-19.34-44.69-43.13-44.69z\" fill=\"#aaa\"/><circle cx=\"169.13\" cy=\"173.57\" r=\"20.96\"/><circle cx=\"180.48\" cy=\"170.39\" r=\"5.78\" fill=\"#fff\"/></g></svg>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "@ApiPlatform/DataCollector/api-platform-icon.svg";
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<svg width=\"24\" height=\"24\" viewBox=\"0 120 256 300\" xmlns=\"http://www.w3.org/2000/svg\"><g data-name=\"spider\"><g fill=\"#aaa\"><path d=\"M40.66 321.08L23.67 248.1l31.32.56 1.77 7.43-24.21-.5 15.36 63.08-7.25 2.41z\"/><path d=\"M36.81 331s-6.21 11.73 5.36 16.56 17-5.51 17-5.51.89-4.49-4.79-14.32-7.38-11.9-10.56-12.43-7.01 15.7-7.01 15.7z\"/></g><g fill=\"#aaa\"><path d=\"M21.27 383.93L0 246.05l57.07-25.51-.73 7.59-47.79 22.31L28.9 384l-7.63-.07z\"/><path d=\"m14.57 396.19s-9.28 13 3.75 20.59 21.17-3.84 21.17-3.84 1.78-5.21-3.43-17.83-6.91-15.4-10.63-16.54-10.86 17.62-10.86 17.62z\"/></g><g fill=\"#aaa\"><path d=\"M215.59 320.08l16.99-72.98-31.32.56-1.77 7.43 24.21-.5-15.36 63.08 7.25 2.41z\"/><path d=\"M219.44 330s6.21 11.73-5.36 16.56-17-5.51-17-5.51-.89-4.49 4.79-14.32 7.4-11.93 10.59-12.46 6.98 15.73 6.98 15.73z\"/></g><g fill=\"#aaa\"><path d=\"M234.98 382.93l21.27-137.88-57.07-25.51.73 7.59 47.8 22.31L227.36 383l7.62-.07z\"/><path d=\"m241.68 395.19s9.28 13-3.75 20.59-21.17-3.84-21.17-3.84-1.78-5.21 3.43-17.83 6.91-15.4 10.63-16.54 10.86 17.62 10.86 17.62z\"/></g><path d=\"m207.23 219.63c0 41-35.77 62.69-79.9 62.69s-77.67-21.69-77.67-62.69 33.54-71.22 77.67-71.22 79.9 30.22 79.9 71.22z\" fill=\"#d2d2d2\"/><path d=\"m126.33 285.72c-23.33 0-43.33-5.79-57.75-16.72-15.46-11.73-23.63-28.79-23.63-49.34 0-38.35 28.14-74.62 81.38-74.62 53.92 0 83.62 36.82 83.62 74.62 0 20.47-8.58 37.54-24.8 49.38-14.78 10.75-35.66 16.68-58.82 16.68zm0-133.91c-49.07 0-74 33.44-74 67.82 0 36.58 28.34 59.3 74 59.3 46.28 0 76.18-23.28 76.18-59.3 0-42.83-41.42-67.82-76.18-67.82z\" fill=\"#aaa\"/><ellipse cx=\"92.29\" cy=\"165.77\" rx=\"37.55\" ry=\"36.82\" fill=\"#fff\"/><path d=\"M91.29 205c-22 0-39.92-17.59-39.92-39.2s17.91-39.2 39.92-39.2 39.93 17.59 39.93 39.2S113.3 205 91.29 205zm0-73.65c-19.39 0-35.17 15.45-35.17 34.45s15.78 34.45 35.17 34.45 35.17-15.45 35.17-34.45-15.78-34.47-35.17-34.47z\" fill=\"#aaa\"/><ellipse cx=\"95.19\" cy=\"167.95\" rx=\"16.99\" ry=\"16.3\"/><circle cx=\"92.02\" cy=\"165.04\" r=\"4.68\" fill=\"#fff\"/><ellipse cx=\"166.29\" cy=\"171.09\" rx=\"45.51\" ry=\"47.07\" fill=\"#fff\"/><path d=\"m165.29 220.53c-26.41 0-47.89-22.18-47.89-49.44s21.48-49.44 47.89-49.44 47.89 22.18 47.89 49.44-21.48 49.44-47.89 49.44zm0-94.13c-23.78 0-43.14 20-43.14 44.69s19.35 44.69 43.14 44.69 43.13-20 43.13-44.69-19.34-44.69-43.13-44.69z\" fill=\"#aaa\"/><circle cx=\"169.13\" cy=\"173.57\" r=\"20.96\"/><circle cx=\"180.48\" cy=\"170.39\" r=\"5.78\" fill=\"#fff\"/></g></svg>
", "@ApiPlatform/DataCollector/api-platform-icon.svg", "C:\\Users\\<USER>\\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\\Bureau\\pfe\\backend_pfe\\vendor\\api-platform\\core\\src\\Symfony\\Bundle\\Resources\\views\\DataCollector\\api-platform-icon.svg");
    }
}
