import React, { useContext } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import ApprenantNavigator from './ApprenantNavigator';
import AuthNavigator from './AuthNavigator';
import { AuthContext } from '../contexts/AuthContext';
import LoadingScreen from '../components/LoadingScreen';

const AppNavigator = () => {
  const { isLoading, userToken, user } = useContext(AuthContext);

  if (isLoading) {
    return <LoadingScreen message="Chargement de l'application..." />;
  }

  // Real authentication flow - same as frontend_pfe
  console.log('AppNavigator - Authentication state:', {
    hasToken: !!userToken,
    hasUser: !!user,
    userRole: user?.role
  });

  return (
    <NavigationContainer>
      {userToken && user ? (
        // User is authenticated, show the apprenant interface
        <ApprenantNavigator />
      ) : (
        // User is not authenticated, show auth screens
        <AuthNavigator />
      )}
    </NavigationContainer>
  );
};

export default AppNavigator;
