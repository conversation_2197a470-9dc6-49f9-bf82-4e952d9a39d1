import React, { createContext, useState, useEffect } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import authService from "../services/authService";
import webSocketService from "../services/webSocketService";
import { Alert } from "react-native";

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [userToken, setUserToken] = useState(null);
  const [user, setUser] = useState(null);
  const [error, setError] = useState(null);

  // Check for existing token on app start
  useEffect(() => {
    const bootstrapAsync = async () => {
      try {
        console.log("Checking for existing authentication...");

        // Get stored token and user data
        const storedToken = await AsyncStorage.getItem("userToken");
        const storedUserData = await AsyncStorage.getItem("userData");

        if (storedToken && storedUserData) {
          console.log("Found existing authentication data");

          // Set the user and token in state
          setUserToken(storedToken);
          setUser(JSON.parse(storedUserData));

          // Connect to WebSocket since user is already authenticated (disabled for now)
          console.log(
            "WebSocket connection disabled - no WebSocket server running"
          );
          // webSocketService.connect();

          // Verify token is still valid with the backend
          try {
            const isValid = await authService.verifyToken();
            if (!isValid) {
              console.log(
                "Stored token is invalid, clearing authentication data"
              );
              await AsyncStorage.removeItem("userToken");
              await AsyncStorage.removeItem("userData");
              setUserToken(null);
              setUser(null);
              // Disconnect WebSocket if token is invalid
              webSocketService.disconnect();
            }
          } catch (verifyError) {
            console.error("Error verifying token:", verifyError);
            // Keep the user logged in even if verification fails
            // This allows offline usage
          }
        } else {
          console.log("No authentication data found");
        }
      } catch (e) {
        console.error("Error checking authentication:", e);
      } finally {
        setIsLoading(false);
      }
    };

    bootstrapAsync();
  }, []);

  // Real login function that connects to the backend
  const login = async (email, password) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log("Attempting login with:", email);

      // Call the auth service to login
      const response = await authService.login(email, password);

      if (response && response.token) {
        console.log("Login successful");

        // Extract user data from response
        const userData = response.user || {
          id: 0,
          email: email,
          name: "Utilisateur",
          role: "apprenant",
        };

        // Store token and user data
        await AsyncStorage.setItem("userToken", response.token);
        await AsyncStorage.setItem("userData", JSON.stringify(userData));

        // Update state
        setUserToken(response.token);
        setUser(userData);

        // Connect to WebSocket after successful login
        console.log("Initializing WebSocket connection...");
        webSocketService.connect();

        return true;
      } else {
        console.error("Login failed: Invalid response format", response);
        setError("Format de réponse invalide");
        return false;
      }
    } catch (e) {
      console.error("Login error:", e);

      // Handle specific error messages
      let errorMessage = "Une erreur est survenue lors de la connexion";

      if (e.response) {
        if (e.response.status === 401) {
          errorMessage = "Identifiants incorrects";
        } else if (e.response.status === 403) {
          errorMessage = "Votre compte est en attente d'approbation";
        } else if (e.response.data && e.response.data.message) {
          errorMessage = e.response.data.message;
        }
      }

      setError(errorMessage);
      Alert.alert("Erreur de Connexion", errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Real logout function
  const logout = async () => {
    console.log("🚪 Logout function called");
    setIsLoading(true);

    try {
      console.log("🔄 Starting logout process...");

      // Call the auth service to logout
      console.log("📡 Calling authService.logout()...");
      const logoutResult = await authService.logout();
      console.log("📡 AuthService logout result:", logoutResult);

      // Clear storage
      console.log("🗑️ Clearing AsyncStorage...");
      await AsyncStorage.removeItem("userToken");
      await AsyncStorage.removeItem("userData");
      console.log("✅ AsyncStorage cleared");

      // Update state
      console.log("🔄 Updating authentication state...");
      setUserToken(null);
      setUser(null);
      console.log("✅ Authentication state cleared");

      // Disconnect from WebSocket after logout
      console.log("🔌 Disconnecting WebSocket...");
      webSocketService.disconnect();
      console.log("✅ WebSocket disconnected");

      console.log("🎉 Logout completed successfully");
    } catch (e) {
      console.error("❌ Error during logout:", e);

      // Even if the server-side logout fails, we still want to clear local storage
      console.log("🗑️ Force clearing storage due to error...");
      await AsyncStorage.removeItem("userToken");
      await AsyncStorage.removeItem("userData");
      setUserToken(null);
      setUser(null);

      // Disconnect from WebSocket even if logout fails
      webSocketService.disconnect();
      console.log("✅ Force logout completed");
    } finally {
      setIsLoading(false);
      console.log("🏁 Logout process finished");
    }
  };

  // Real register function
  const register = async (userData) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log("Registering with:", userData);

      // Make sure the role is set to apprenant
      const registerData = {
        ...userData,
        role: "apprenant",
      };

      // Call the auth service to register
      const response = await authService.register(registerData);

      if (response && response.success) {
        return {
          success: true,
          message: response.message || "Inscription réussie",
          user: response.user,
        };
      } else {
        setError(response?.message || "Erreur lors de l'inscription");
        return {
          success: false,
          message: response?.message || "Erreur lors de l'inscription",
        };
      }
    } catch (e) {
      console.error("Register error:", e);

      let errorMessage = "Une erreur est survenue lors de l'inscription";

      if (e.response && e.response.data) {
        if (e.response.data.message) {
          errorMessage = e.response.data.message;
        } else if (e.response.data.errors) {
          // Handle validation errors
          const errors = Object.values(e.response.data.errors).flat();
          errorMessage = errors.join(", ");
        }
      }

      setError(errorMessage);
      return {
        success: false,
        message: errorMessage,
      };
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isLoading,
        userToken,
        user,
        error,
        login,
        logout,
        register,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
