<?php

namespace Proxies\__CG__\App\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Evenement extends \App\Entity\Evenement implements \Doctrine\ORM\Proxy\InternalProxy
{
    use \Symfony\Component\VarExporter\LazyGhostTrait {
        initializeLazyObject as private;
        setLazyObjectAsInitialized as public __setInitialized;
        isLazyObjectInitialized as private;
        createLazyGhost as private;
        resetLazyObject as private;
    }

    public function __load(): void
    {
        $this->initializeLazyObject();
    }
    

    private const LAZY_OBJECT_PROPERTY_SCOPES = [
        "\0".parent::class."\0".'administrateurs' => [parent::class, 'administrateurs', null, 16],
        "\0".parent::class."\0".'categorie' => [parent::class, 'categorie', null, 16],
        "\0".parent::class."\0".'couleur' => [parent::class, 'couleur', null, 16],
        "\0".parent::class."\0".'dateDebut' => [parent::class, 'dateDebut', null, 16],
        "\0".parent::class."\0".'dateFin' => [parent::class, 'dateFin', null, 16],
        "\0".parent::class."\0".'description' => [parent::class, 'description', null, 16],
        "\0".parent::class."\0".'id' => [parent::class, 'id', null, 16],
        "\0".parent::class."\0".'journeeEntiere' => [parent::class, 'journeeEntiere', null, 16],
        "\0".parent::class."\0".'notifications' => [parent::class, 'notifications', null, 16],
        "\0".parent::class."\0".'titre' => [parent::class, 'titre', null, 16],
        'administrateurs' => [parent::class, 'administrateurs', null, 16],
        'categorie' => [parent::class, 'categorie', null, 16],
        'couleur' => [parent::class, 'couleur', null, 16],
        'dateDebut' => [parent::class, 'dateDebut', null, 16],
        'dateFin' => [parent::class, 'dateFin', null, 16],
        'description' => [parent::class, 'description', null, 16],
        'id' => [parent::class, 'id', null, 16],
        'journeeEntiere' => [parent::class, 'journeeEntiere', null, 16],
        'notifications' => [parent::class, 'notifications', null, 16],
        'titre' => [parent::class, 'titre', null, 16],
    ];

    public function __isInitialized(): bool
    {
        return isset($this->lazyObjectState) && $this->isLazyObjectInitialized();
    }

    public function __serialize(): array
    {
        $properties = (array) $this;
        unset($properties["\0" . self::class . "\0lazyObjectState"]);

        return $properties;
    }
}
