{"name": "mobile_pfe", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/ngrok": "^4.1.3", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@react-navigation/stack": "^7.3.2", "axios": "^1.9.0", "expo": "~53.0.9", "expo-document-picker": "^13.1.5", "expo-image-picker": "^16.1.4", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "^2.25.0", "react-native-paper": "^5.11.1", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "^18.0.0"}, "private": true}