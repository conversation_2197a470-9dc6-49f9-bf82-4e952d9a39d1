import React, { useContext } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { ThemeContext } from '../contexts/ThemeContext';

const MessageItem = ({ 
  message, 
  isMe, 
  showAvatar = true, 
  time, 
  isFile = false, 
  onFilePress 
}) => {
  const { theme, isDarkMode } = useContext(ThemeContext);
  
  const renderFileAttachment = () => {
    if (!isFile) return null;
    
    return (
      <TouchableOpacity 
        style={styles.fileContainer}
        onPress={() => onFilePress && onFilePress(message)}
      >
        <MaterialCommunityIcons name="file-document-outline" size={24} color={theme.primary} />
        <Text style={[styles.fileName, { color: theme.primary }]} numberOfLines={1}>
          {message.fileName || 'Document'}
        </Text>
      </TouchableOpacity>
    );
  };
  
  return (
    <View style={[
      styles.container,
      isMe ? styles.myMessageContainer : styles.otherMessageContainer
    ]}>
      {!isMe && showAvatar && (
        <Image 
          source={
            message.avatar 
              ? { uri: message.avatar } 
              : require('../../assets/default-avatar.png')
          } 
          style={styles.avatar} 
        />
      )}
      
      <View style={[
        styles.messageContent,
        isMe 
          ? [styles.myMessage, { backgroundColor: theme.primary }] 
          : [styles.otherMessage, { backgroundColor: isDarkMode ? theme.background : '#F3F4F6' }],
        isFile && styles.fileMessage
      ]}>
        {isFile ? (
          renderFileAttachment()
        ) : (
          <Text style={[
            styles.messageText,
            isMe 
              ? [styles.myMessageText, { color: theme.text.inverse }] 
              : [styles.otherMessageText, { color: theme.text.primary }]
          ]}>
            {message.content}
          </Text>
        )}
        
        <Text style={[
          styles.timeText,
          isMe 
            ? [styles.myTimeText, { color: 'rgba(255, 255, 255, 0.7)' }] 
            : [styles.otherTimeText, { color: theme.text.tertiary }]
        ]}>
          {time}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  myMessageContainer: {
    justifyContent: 'flex-end',
  },
  otherMessageContainer: {
    justifyContent: 'flex-start',
  },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 8,
  },
  messageContent: {
    maxWidth: '75%',
    borderRadius: 16,
    padding: 12,
    marginBottom: 4,
  },
  myMessage: {
    borderBottomRightRadius: 4,
  },
  otherMessage: {
    borderBottomLeftRadius: 4,
  },
  fileMessage: {
    padding: 8,
  },
  messageText: {
    fontSize: 16,
  },
  myMessageText: {
  },
  otherMessageText: {
  },
  timeText: {
    fontSize: 10,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  myTimeText: {
  },
  otherTimeText: {
  },
  fileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  fileName: {
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
});

export default MessageItem;
