import AsyncStorage from '@react-native-async-storage/async-storage';

class WebSocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.listeners = [];
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectTimeout = null;
    this.wsUrl = 'wss://127.0.0.1:8080'; // Same as frontend_pfe
  }

  /**
   * Initialise la connexion WebSocket
   */
  connect() {
    if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
      console.log('WebSocket already connected or connecting');
      return;
    }

    try {
      console.log(`Connecting to WebSocket server: ${this.wsUrl}`);
      this.socket = new WebSocket(this.wsUrl);

      this.socket.onopen = this.handleOpen.bind(this);
      this.socket.onmessage = this.handleMessage.bind(this);
      this.socket.onclose = this.handleClose.bind(this);
      this.socket.onerror = this.handleError.bind(this);
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      this.scheduleReconnect();
    }
  }

  /**
   * Gère l'ouverture de la connexion
   */
  handleOpen() {
    console.log('WebSocket connection established');
    this.isConnected = true;
    this.reconnectAttempts = 0;

    // Authenticate the user
    this.authenticate();
  }

  /**
   * Authentifie l'utilisateur auprès du serveur WebSocket
   */
  async authenticate() {
    try {
      const token = await AsyncStorage.getItem('userToken');
      const userData = await AsyncStorage.getItem('userData');
      
      if (token && userData) {
        const user = JSON.parse(userData);
        this.send({
          type: 'auth',
          token: token,
          userId: user.id
        });
        console.log('WebSocket authentication sent');
      } else {
        console.warn('Cannot authenticate: no user logged in');
      }
    } catch (error) {
      console.error('Error during WebSocket authentication:', error);
    }
  }

  /**
   * Gère les messages reçus
   */
  handleMessage(event) {
    try {
      const data = JSON.parse(event.data);
      console.log('WebSocket message received:', data);

      // Notify all listeners
      this.notifyListeners(data);
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  /**
   * Gère la fermeture de la connexion
   */
  handleClose(event) {
    console.log('WebSocket connection closed:', event.code, event.reason);
    this.isConnected = false;
    this.socket = null;

    // Attempt to reconnect if it wasn't a clean close
    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect();
    }
  }

  /**
   * Gère les erreurs de connexion
   */
  handleError(error) {
    console.error('WebSocket error:', error);
    this.isConnected = false;
  }

  /**
   * Programme une tentative de reconnexion
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnection attempts reached');
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    this.reconnectAttempts++;

    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);

    this.reconnectTimeout = setTimeout(() => {
      console.log(`Reconnection attempt ${this.reconnectAttempts}`);
      this.connect();
    }, delay);
  }

  /**
   * Envoie un message au serveur WebSocket
   */
  send(data) {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      console.warn('Cannot send message: WebSocket not connected');
      return false;
    }

    try {
      this.socket.send(JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Error sending WebSocket message:', error);
      return false;
    }
  }

  /**
   * Ferme la connexion WebSocket
   */
  disconnect() {
    if (this.socket) {
      this.socket.close(1000, 'Voluntary disconnection');
      this.socket = null;
    }

    this.isConnected = false;
    clearTimeout(this.reconnectTimeout);
  }

  /**
   * Ajoute un écouteur pour les messages WebSocket
   */
  addListener(callback) {
    if (typeof callback === 'function' && !this.listeners.includes(callback)) {
      this.listeners.push(callback);
      return true;
    }
    return false;
  }

  /**
   * Supprime un écouteur
   */
  removeListener(callback) {
    const index = this.listeners.indexOf(callback);
    if (index !== -1) {
      this.listeners.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * Notifie tous les écouteurs d'un nouveau message
   */
  notifyListeners(data) {
    this.listeners.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in WebSocket listener:', error);
      }
    });
  }

  /**
   * Vérifie si la connexion est active
   */
  isConnectedToServer() {
    return this.isConnected && this.socket && this.socket.readyState === WebSocket.OPEN;
  }

  /**
   * Initialise l'écoute des notifications en temps réel
   */
  initializeRealTimeNotifications(callback) {
    // Connect to WebSocket server
    this.connect();

    // Add listener for notifications
    const listener = (data) => {
      if (data.type === "notification") {
        console.log("New notification received via WebSocket:", data.data);

        // Call the callback with the notification
        if (typeof callback === "function") {
          callback(data.data);
        }
      }
    };

    // Add the listener
    this.addListener(listener);

    // Return a function to stop listening
    return () => {
      this.removeListener(listener);
    };
  }
}

// Export a single instance of the service
const webSocketService = new WebSocketService();
export default webSocketService;
