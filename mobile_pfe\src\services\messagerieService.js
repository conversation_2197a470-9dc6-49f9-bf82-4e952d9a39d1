import api from './api';

const messagerieService = {
  // Get all conversations for an apprenant - real API implementation matching frontend_pfe
  getConversations: async () => {
    try {
      console.log('Getting conversations from real API');

      // First, get the current user to get their ID (same as frontend_pfe)
      const userResponse = await api.get('/user/me');
      const currentUser = userResponse.data;

      console.log('Current user for messaging:', currentUser);

      // Check for user ID in different possible fields
      const userId = currentUser?.id || currentUser?.userId || currentUser?.user?.id;

      if (!currentUser || !userId) {
        console.error('Invalid user data:', currentUser);
        console.error('Available user fields:', Object.keys(currentUser || {}));
        throw new Error('Impossible de récupérer les informations utilisateur');
      }

      // Use the correct endpoint for apprenant conversations
      const response = await api.get(`/messagerie/apprenant/${userId}/conversations`);

      console.log('Conversations API response:', response.data);

      // Handle different response formats
      const conversations = response.data?.conversations || response.data || [];

      // Process conversations to match the expected format (same as frontend_pfe)
      const processedConversations = (Array.isArray(conversations) ? conversations : []).map(conv => {
        return {
          id: conv.formateur_id,
          name: conv.formateur_name,
          avatar: conv.formateur_image || null,
          lastMessage: conv.message,
          lastMessageTime: conv.date,
          time: formatTime(conv.date),
          unread: parseInt(conv.unread_count) || 0,
          status: 'online', // Default status
          type: 'text'
        };
      });

      return processedConversations;
    } catch (error) {
      console.error('Error fetching conversations:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      if (error.response?.status === 404) {
        // No conversations found, return empty array
        return [];
      }

      throw new Error('Impossible de récupérer vos conversations');
    }
  },

  // Get messages for a specific conversation - real API implementation
  getMessages: async (formateurId) => {
    try {
      console.log(`Getting messages for formateur ${formateurId} from real API`);

      // First, get the current user to get their ID
      const userResponse = await api.get('/user/me');
      const currentUser = userResponse.data;

      // Check for user ID in different possible fields
      const userId = currentUser?.id || currentUser?.userId || currentUser?.user?.id;

      if (!currentUser || !userId) {
        throw new Error('Impossible de récupérer les informations utilisateur');
      }

      // Use the correct endpoint pattern from backend
      const response = await api.get(`/messagerie/formateur/${formateurId}/apprenant/${userId}`);

      console.log('Messages API response:', response.data);

      // Handle different response formats
      const messages = response.data?.messages || response.data || [];

      // Process messages to add isMe flag, isRead flag, and time formatting
      const processedMessages = (Array.isArray(messages) ? messages : []).map(msg => {
        return {
          ...msg,
          isMe: !msg.sentByFormateur, // If not sent by formateur, then sent by apprenant (me)
          isRead: msg.lu, // Map backend 'lu' field to 'isRead'
          content: msg.message, // Map backend 'message' field to 'content'
          time: formatTime(msg.date || msg.timestamp || msg.createdAt)
        };
      });

      return processedMessages;
    } catch (error) {
      console.error('Error fetching messages:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      if (error.response?.status === 404) {
        // No messages found, return empty array
        return [];
      }

      throw new Error('Impossible de récupérer les messages');
    }
  },

  // Send a message to a formateur - real API implementation
  sendMessage: async (formateurId, content, fileAttachment = null) => {
    try {
      console.log(`Sending message to formateur ${formateurId} with real API`);
      console.log('Message content:', content);

      // First, get the current user to get their ID
      const userResponse = await api.get('/user/me');
      const currentUser = userResponse.data;
      console.log('Current user for sending message:', currentUser);

      // Check for user ID in different possible fields
      const userId = currentUser?.id || currentUser?.userId || currentUser?.user?.id;
      console.log('Extracted userId:', userId);

      if (!currentUser || !userId) {
        console.error('Failed to get user ID:', { currentUser, userId });
        throw new Error('Impossible de récupérer les informations utilisateur');
      }

      // Prepare the message data
      const messageData = {
        message: content // Backend expects 'message' field
      };

      // If there's a file attachment, handle it (for now, just log it)
      if (fileAttachment) {
        console.log('File attachment detected:', fileAttachment.name);
        // TODO: Implement file upload functionality
        messageData.hasAttachment = true;
        messageData.attachmentName = fileAttachment.name;
      }

      console.log('Sending message data:', messageData);
      console.log('API endpoint:', `/messagerie/apprenant/${userId}/formateur/${formateurId}/envoyer`);

      // Use the correct endpoint pattern from backend
      const response = await api.post(`/messagerie/apprenant/${userId}/formateur/${formateurId}/envoyer`, messageData);

      console.log('Send message API response:', response.data);

      const newMessage = {
        ...response.data.data,
        isMe: true,
        time: formatTime(response.data.data?.date || response.data.data?.timestamp)
      };

      console.log('Processed new message:', newMessage);

      return {
        success: true,
        message: 'Message envoyé avec succès',
        data: newMessage
      };
    } catch (error) {
      console.error('Error sending message:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      if (error.response?.status === 404) {
        throw new Error('Formateur non trouvé');
      }

      if (error.response?.status === 400) {
        throw new Error('Données invalides');
      }

      // Include more specific error message from backend
      const errorMessage = error.response?.data?.message || error.response?.data?.error || 'Impossible d\'envoyer le message';
      throw new Error(errorMessage);
    }
  },

  // Mark messages as read - FIXED: Use correct backend endpoint
  markAsRead: async (messageIds) => {
    try {
      console.log(`Marking messages as read: ${messageIds.join(', ')}`);

      // FIXED: Backend uses individual message endpoints /messagerie/{id}/marquer-lu
      // Mark each message individually as the backend expects
      const promises = messageIds.map(messageId =>
        api.put(`/messagerie/${messageId}/marquer-lu`)
      );

      const responses = await Promise.all(promises);

      console.log('Mark as read API responses:', responses.map(r => r.data));

      return {
        success: true,
        message: 'Messages marqués comme lus',
        messageIds: messageIds
      };
    } catch (error) {
      console.error('Error marking messages as read:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      throw new Error('Impossible de marquer les messages comme lus');
    }
  },

  // Get available formateurs for messaging - real API implementation
  getFormateurs: async () => {
    try {
      console.log('Getting formateurs from real API');

      // First, get the current user to get their ID
      const userResponse = await api.get('/user/me');
      const currentUser = userResponse.data;

      // Check for user ID in different possible fields
      const userId = currentUser?.id || currentUser?.userId || currentUser?.user?.id;

      if (!currentUser || !userId) {
        throw new Error('Impossible de récupérer les informations utilisateur');
      }

      // Use the correct endpoint pattern from backend
      const response = await api.get(`/messagerie/apprenant/${userId}/formateurs`);

      console.log('Formateurs API response:', response.data);

      // Handle different response formats
      const formateurs = response.data?.formateurs || response.data || [];

      return Array.isArray(formateurs) ? formateurs : [];
    } catch (error) {
      console.error('Error fetching formateurs:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      if (error.response?.status === 404) {
        // No formateurs found, return empty array
        return [];
      }

      throw new Error('Impossible de récupérer la liste des formateurs');
    }
  }
};

// Helper function to format timestamps for display
const formatTime = (timestamp) => {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const now = new Date();
  const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    // Today: show time
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays === 1) {
    // Yesterday
    return 'Hier';
  } else if (diffDays < 7) {
    // This week: show day name
    return date.toLocaleDateString([], { weekday: 'long' });
  } else {
    // Older: show date
    return date.toLocaleDateString([], { day: 'numeric', month: 'short' });
  }
};

export default messagerieService;
