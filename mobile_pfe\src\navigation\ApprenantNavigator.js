import React, { useContext } from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { ThemeContext } from '../contexts/ThemeContext';

// Screens
import DashboardScreen from '../screens/Apprenant/DashboardScreen';
import CoursesScreen from '../screens/Apprenant/CoursesScreen';
import CourseDetailScreen from '../screens/Apprenant/CourseDetailScreen';
import ConversationListScreen from '../screens/Apprenant/ConversationListScreen';
import ConversationDetailScreen from '../screens/Apprenant/ConversationDetailScreen';
import ReclamationScreen from '../screens/Apprenant/ReclamationScreen';
import ReclamationDetailScreen from '../screens/Apprenant/ReclamationDetailScreen';
import QuizScreen from '../screens/Apprenant/QuizScreen';
import ProfileScreen from '../screens/Apprenant/ProfileScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Stack navigators for each tab
const DashboardStack = () => {
  const { theme } = useContext(ThemeContext);

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.card,
          elevation: 0,
          shadowOpacity: 0,
          borderBottomWidth: 1,
          borderBottomColor: theme.border,
        },
        headerTitleStyle: {
          color: theme.text.primary,
          fontWeight: 'bold',
        },
        headerTintColor: theme.text.primary,
      }}
    >
      <Stack.Screen
        name="DashboardMain"
        component={DashboardScreen}
        options={{ title: 'Tableau de bord' }}
      />
    </Stack.Navigator>
  );
};

const CoursesStack = () => {
  const { theme } = useContext(ThemeContext);

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.card,
          elevation: 0,
          shadowOpacity: 0,
          borderBottomWidth: 1,
          borderBottomColor: theme.border,
        },
        headerTitleStyle: {
          color: theme.text.primary,
          fontWeight: 'bold',
        },
        headerTintColor: theme.text.primary,
      }}
    >
      <Stack.Screen
        name="CoursesMain"
        component={CoursesScreen}
        options={{ title: 'Mes cours' }}
      />
      <Stack.Screen
        name="CourseDetail"
        component={CourseDetailScreen}
        options={({ route }) => ({ title: route.params?.title || 'Détails du cours' })}
      />
      <Stack.Screen
        name="Quiz"
        component={QuizScreen}
        options={({ route }) => ({ title: route.params?.quiz?.title || 'Quiz' })}
      />
    </Stack.Navigator>
  );
};

const MessagerieStack = () => {
  const { theme } = useContext(ThemeContext);

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.card,
          elevation: 0,
          shadowOpacity: 0,
          borderBottomWidth: 1,
          borderBottomColor: theme.border,
        },
        headerTitleStyle: {
          color: theme.text.primary,
          fontWeight: 'bold',
        },
        headerTintColor: theme.text.primary,
      }}
    >
      <Stack.Screen
        name="MessagerieMain"
        component={ConversationListScreen}
        options={{ title: 'Messagerie' }}
      />
      <Stack.Screen
        name="ConversationDetail"
        component={ConversationDetailScreen}
        options={({ route }) => ({
          title: route.params?.name || 'Conversation',
        })}
      />
    </Stack.Navigator>
  );
};

const ReclamationStack = () => {
  const { theme } = useContext(ThemeContext);

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.card,
          elevation: 0,
          shadowOpacity: 0,
          borderBottomWidth: 1,
          borderBottomColor: theme.border,
        },
        headerTitleStyle: {
          color: theme.text.primary,
          fontWeight: 'bold',
        },
        headerTintColor: theme.text.primary,
      }}
    >
      <Stack.Screen
        name="ReclamationMain"
        component={ReclamationScreen}
        options={{ title: 'Réclamations' }}
      />
      <Stack.Screen
        name="ReclamationDetail"
        component={ReclamationDetailScreen}
        options={({ route }) => ({ title: route.params?.title || 'Détails de la réclamation' })}
      />
    </Stack.Navigator>
  );
};

const ProfileStack = () => {
  const { theme } = useContext(ThemeContext);

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.card,
          elevation: 0,
          shadowOpacity: 0,
          borderBottomWidth: 1,
          borderBottomColor: theme.border,
        },
        headerTitleStyle: {
          color: theme.text.primary,
          fontWeight: 'bold',
        },
        headerTintColor: theme.text.primary,
      }}
    >
      <Stack.Screen
        name="ProfileMain"
        component={ProfileScreen}
        options={{ title: 'Mon profil' }}
      />
    </Stack.Navigator>
  );
};

const ApprenantNavigator = () => {
  const { theme } = useContext(ThemeContext);

  return (
    <Tab.Navigator
      initialRouteName="Dashboard"
      screenOptions={{
        tabBarActiveTintColor: theme.tabBarActive,
        tabBarInactiveTintColor: theme.tabBarInactive,
        tabBarStyle: {
          backgroundColor: theme.tabBar,
          borderTopColor: theme.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        headerShown: false,
      }}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardStack}
        options={{
          tabBarLabel: 'Tableau de bord',
          tabBarIcon: ({ color, size }) => (
            <MaterialCommunityIcons name="view-dashboard" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="Courses"
        component={CoursesStack}
        options={{
          tabBarLabel: 'Cours',
          tabBarIcon: ({ color, size }) => (
            <MaterialCommunityIcons name="book-open-variant" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="Messagerie"
        component={MessagerieStack}
        options={{
          tabBarLabel: 'Messages',
          tabBarIcon: ({ color, size }) => (
            <MaterialCommunityIcons name="message-text" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="Reclamation"
        component={ReclamationStack}
        options={{
          tabBarLabel: 'Réclamations',
          tabBarIcon: ({ color, size }) => (
            <MaterialCommunityIcons name="alert-circle" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileStack}
        options={{
          tabBarLabel: 'Profil',
          tabBarIcon: ({ color, size }) => (
            <MaterialCommunityIcons name="account" color={color} size={size} />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

export default ApprenantNavigator;
