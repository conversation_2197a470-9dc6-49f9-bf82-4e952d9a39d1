<?php

/**
 * This file has been auto-generated
 * by the Symfony Routing Component.
 */

return [
    false, // $matchHost
    [ // $staticRoutes
        '/api/evenement/debug' => [[['_route' => 'api_evenement_debug', '_controller' => 'App\\Controller\\EvenementController::debug'], null, ['GET' => 0], null, false, false, null]],
        '/api/evenement/date-range' => [[['_route' => 'api_evenement_by_date_range', '_controller' => 'App\\Controller\\EvenementController::getByDateRange'], null, ['GET' => 0], null, false, false, null]],
        '/api/evenement/upcoming' => [[['_route' => 'api_evenement_upcoming', '_controller' => 'App\\Controller\\EvenementController::getUpcoming'], null, ['GET' => 0], null, false, false, null]],
        '/api/test' => [[['_route' => 'api_test', '_controller' => 'App\\Controller\\AuthController::test'], null, ['GET' => 0], null, false, false, null]],
        '/api/logout' => [[['_route' => 'api_logout', '_controller' => 'App\\Controller\\AuthController::logout'], null, ['POST' => 0], null, false, false, null]],
        '/_profiler' => [[['_route' => '_profiler_home', '_controller' => 'web_profiler.controller.profiler::homeAction'], null, null, null, true, false, null]],
        '/_profiler/search' => [[['_route' => '_profiler_search', '_controller' => 'web_profiler.controller.profiler::searchAction'], null, null, null, false, false, null]],
        '/_profiler/search_bar' => [[['_route' => '_profiler_search_bar', '_controller' => 'web_profiler.controller.profiler::searchBarAction'], null, null, null, false, false, null]],
        '/_profiler/phpinfo' => [[['_route' => '_profiler_phpinfo', '_controller' => 'web_profiler.controller.profiler::phpinfoAction'], null, null, null, false, false, null]],
        '/_profiler/xdebug' => [[['_route' => '_profiler_xdebug', '_controller' => 'web_profiler.controller.profiler::xdebugAction'], null, null, null, false, false, null]],
        '/_profiler/open' => [[['_route' => '_profiler_open_file', '_controller' => 'web_profiler.controller.profiler::openAction'], null, null, null, false, false, null]],
        '/api/actions' => [
            [['_route' => 'api_action_list', '_controller' => 'App\\Controller\\ActionController::list'], null, ['GET' => 0], null, false, false, null],
            [['_route' => 'api_action_create', '_controller' => 'App\\Controller\\ActionController::create'], null, ['POST' => 0], null, false, false, null],
        ],
        '/api/admin/users' => [[['_route' => 'api_admin_users', '_controller' => 'App\\Controller\\AdminController::getApprovedUsers'], null, ['GET' => 0, 'OPTIONS' => 1], null, false, false, null]],
        '/api/admin/users/pending' => [[['_route' => 'api_admin_users_pending', '_controller' => 'App\\Controller\\AdminController::getPendingUsers'], null, ['GET' => 0, 'OPTIONS' => 1], null, false, false, null]],
        '/api/admin/users/add' => [[['_route' => 'api_admin_user_add', '_controller' => 'App\\Controller\\AdminController::addUser'], null, ['POST' => 0, 'OPTIONS' => 1], null, false, false, null]],
        '/api/admin/apprenants' => [[['_route' => 'api_admin_apprenants', '_controller' => 'App\\Controller\\AdminController::getApprenants'], null, ['GET' => 0], null, false, false, null]],
        '/api/apprenant/cours' => [[['_route' => 'api_apprenant_cours', '_controller' => 'App\\Controller\\ApprenantController::getMesCours'], null, ['GET' => 0], null, false, false, null]],
        '/api/register' => [[['_route' => 'api_register', '_controller' => 'App\\Controller\\AuthController::register'], null, ['POST' => 0, 'OPTIONS' => 1], null, false, false, null]],
        '/api/login' => [[['_route' => 'api_login', '_controller' => 'App\\Controller\\AuthController::login'], null, ['POST' => 0, 'OPTIONS' => 1], null, false, false, null]],
        '/api/user/me' => [[['_route' => 'api_user_me', '_controller' => 'App\\Controller\\AuthController::getCurrentUser'], null, ['GET' => 0, 'OPTIONS' => 1], null, false, false, null]],
        '/api/forgot-password' => [[['_route' => 'api_forgot_password', '_controller' => 'App\\Controller\\AuthController::forgotPassword'], null, ['POST' => 0, 'OPTIONS' => 1], null, false, false, null]],
        '/api/certificat' => [[['_route' => 'api_certificat_list', '_controller' => 'App\\Controller\\CertificatController::list'], null, ['GET' => 0], null, false, false, null]],
        '/api/competence' => [[['_route' => 'api_competence_list', '_controller' => 'App\\Controller\\CompetenceController::list'], null, ['GET' => 0], null, false, false, null]],
        '/api/cours' => [
            [['_route' => 'api_cours_index', '_controller' => 'App\\Controller\\CourseController::index'], null, ['GET' => 0], null, false, false, null],
            [['_route' => 'api_cours_create', '_controller' => 'App\\Controller\\CourseController::create'], null, ['POST' => 0], null, false, false, null],
        ],
        '/api/dashboard/test-pending-users' => [[['_route' => 'api_test_pending_users', '_controller' => 'App\\Controller\\DashboardController::testPendingUsers'], null, ['GET' => 0], null, false, false, null]],
        '/api/dashboard/stats' => [[['_route' => 'api_dashboard_stats', '_controller' => 'App\\Controller\\DashboardController::getStats'], null, ['GET' => 0], null, false, false, null]],
        '/api/dashboard/formateur/stats' => [[['_route' => 'api_dashboard_formateur_stats', '_controller' => 'App\\Controller\\DashboardController::getFormateurStats'], null, ['GET' => 0], null, false, false, null]],
        '/api/evaluation' => [
            [['_route' => 'api_evaluation_list', '_controller' => 'App\\Controller\\EvaluationController::list'], null, ['GET' => 0], null, false, false, null],
            [['_route' => 'api_evaluation_create', '_controller' => 'App\\Controller\\EvaluationController::create'], null, ['POST' => 0], null, false, false, null],
        ],
        '/api/evaluation-detail' => [[['_route' => 'api_evaluation_detail_create', '_controller' => 'App\\Controller\\EvaluationDetailController::create'], null, ['POST' => 0], null, false, false, null]],
        '/api/evenement' => [
            [['_route' => 'api_evenement_list', '_controller' => 'App\\Controller\\EvenementController::list'], null, ['GET' => 0], null, false, false, null],
            [['_route' => 'api_evenement_create', '_controller' => 'App\\Controller\\EvenementController::create'], null, ['POST' => 0], null, false, false, null],
        ],
        '/api/upload/profile-image' => [[['_route' => 'api_upload_profile_image', '_controller' => 'App\\Controller\\FileUploadController::uploadProfileImage'], null, ['POST' => 0], null, false, false, null]],
        '/api/fix' => [[['_route' => 'api_fix', '_controller' => 'App\\Controller\\FixController::fix'], null, null, null, false, false, null]],
        '/api/formateur/apprenants' => [[['_route' => 'api_formateur_apprenants', '_controller' => 'App\\Controller\\FormateurController::getApprenants'], null, ['GET' => 0], null, false, false, null]],
        '/api/formateur/cours' => [[['_route' => 'api_formateur_cours', '_controller' => 'App\\Controller\\FormateurController::getCours'], null, ['GET' => 0], null, false, false, null]],
        '/api/notification' => [[['_route' => 'api_notification_list', '_controller' => 'App\\Controller\\NotificationController::getUserNotifications'], null, ['GET' => 0], null, false, false, null]],
        '/api/notification/mark-all-read' => [[['_route' => 'api_notification_mark_all_read', '_controller' => 'App\\Controller\\NotificationController::markAllAsRead'], null, ['PUT' => 0], null, false, false, null]],
        '/api/progression' => [[['_route' => 'api_progression_list', '_controller' => 'App\\Controller\\ProgressionController::list'], null, ['GET' => 0], null, false, false, null]],
        '/api/quiz/batch' => [[['_route' => 'api_quiz_create_batch', '_controller' => 'App\\Controller\\QuizController::createBatch'], null, ['POST' => 0], null, false, false, null]],
        '/api/quiz' => [[['_route' => 'api_quiz_list', '_controller' => 'App\\Controller\\QuizController::list'], null, ['GET' => 0], null, false, false, null]],
        '/api/quiz/competence/create' => [[['_route' => 'api_competence_create', '_controller' => 'App\\Controller\\QuizController::createCompetence'], null, ['POST' => 0], null, false, false, null]],
        '/api/quiz/action/create' => [[['_route' => 'api_quiz_action_create_legacy', '_controller' => 'App\\Controller\\QuizController::createAction'], null, ['POST' => 0], null, false, false, null]],
        '/api/quiz/quiz-action/create' => [[['_route' => 'api_quiz_action_create', '_controller' => 'App\\Controller\\QuizController::createQuizAction'], null, ['POST' => 0], null, false, false, null]],
        '/api/reclamation' => [
            [['_route' => 'api_reclamation_list', '_controller' => 'App\\Controller\\ReclamationController::list'], null, ['GET' => 0], null, false, false, null],
            [['_route' => 'api_reclamation_create', '_controller' => 'App\\Controller\\ReclamationController::create'], null, ['POST' => 0], null, false, false, null],
        ],
        '/api/reclamation/user' => [[['_route' => 'api_reclamation_user', '_controller' => 'App\\Controller\\ReclamationController::getUserReclamations'], null, ['GET' => 0], null, false, false, null]],
        '/api/sous-competence' => [
            [['_route' => 'api_sous_competence_list', '_controller' => 'App\\Controller\\SousCompetenceController::list'], null, ['GET' => 0], null, false, false, null],
            [['_route' => 'api_sous_competence_create', '_controller' => 'App\\Controller\\SousCompetenceController::create'], null, ['POST' => 0], null, false, false, null],
        ],
    ],
    [ // $regexpList
        0 => '{^(?'
                .'|/api(?'
                    .'|/(?'
                        .'|e(?'
                            .'|venement/administrateur/([^/]++)(*:54)'
                            .'|rrors/(\\d+)(*:72)'
                        .')'
                        .'|\\.well\\-known/genid/([^/]++)(*:108)'
                        .'|validation_errors/([^/]++)(*:142)'
                    .')'
                    .'|(?:/(index)(?:\\.([^/]++))?)?(*:179)'
                    .'|/(?'
                        .'|d(?'
                            .'|ocs(?:\\.([^/]++))?(*:213)'
                            .'|iagnostic/c(?'
                                .'|heck\\-(?'
                                    .'|apprenant/([^/]++)(*:262)'
                                    .'|cours/([^/]++)(*:284)'
                                    .'|progression/([^/]++)/([^/]++)(*:321)'
                                .')'
                                .'|reate\\-progression/([^/]++)/([^/]++)(*:366)'
                            .')'
                        .')'
                        .'|c(?'
                            .'|o(?'
                                .'|ntexts/([^.]+)(?:\\.(jsonld))?(*:413)'
                                .'|mpetence/([^/]++)(*:438)'
                                .'|urs/([^/]++)(?'
                                    .'|(*:461)'
                                .')'
                            .')'
                            .'|ertificat/(?'
                                .'|check\\-and\\-generate/([^/]++)/([^/]++)(*:522)'
                                .'|([^/]++)(*:538)'
                                .'|generate(?'
                                    .'|\\-direct(*:565)'
                                    .'|(*:573)'
                                .')'
                                .'|apprenant/([^/]++)(?'
                                    .'|(*:603)'
                                    .'|/cours/([^/]++)(*:626)'
                                .')'
                                .'|([^/]++)/d(?'
                                    .'|ownload(*:655)'
                                    .'|ata(*:666)'
                                .')'
                            .')'
                        .')'
                        .'|validation_errors/([^/]++)(?'
                            .'|(*:706)'
                        .')'
                        .'|a(?'
                            .'|ctions/([^/]++)(?'
                                .'|(*:737)'
                            .')'
                            .'|dmin/(?'
                                .'|users/(?'
                                    .'|approve/([^/]++)(*:779)'
                                    .'|reject/([^/]++)(*:802)'
                                    .'|edit/([^/]++)(*:823)'
                                    .'|delete/([^/]++)(*:846)'
                                .')'
                                .'|apprenants/([^/]++)/cours(?'
                                    .'|(*:883)'
                                    .'|/([^/]++)(?'
                                        .'|(*:903)'
                                    .')'
                                .')'
                            .')'
                        .')'
                        .'|user/([^/]++)(?'
                            .'|(*:931)'
                            .'|/delete(*:946)'
                        .')'
                        .'|re(?'
                            .'|set\\-password/([^/]++)(*:982)'
                            .'|clamation/([^/]++)(?'
                                .'|(*:1011)'
                                .'|/reply(*:1026)'
                            .')'
                        .')'
                        .'|ev(?'
                            .'|aluation(?'
                                .'|/(?'
                                    .'|([^/]++)(*:1065)'
                                    .'|idmodule/([^/]++)(?'
                                        .'|(*:1094)'
                                        .'|/apprenant/([^/]++)(*:1122)'
                                    .')'
                                    .'|quiz/([^/]++)/apprenant/([^/]++)(*:1164)'
                                .')'
                                .'|\\-detail/quiz/([^/]++)/apprenant/([^/]++)(*:1215)'
                            .')'
                            .'|enement/([^/]++)(?'
                                .'|(*:1244)'
                                .'|/administrateur/([^/]++)(?'
                                    .'|(*:1280)'
                                .')'
                            .')'
                        .')'
                        .'|formateur/apprenants/([^/]++)(?'
                            .'|(*:1324)'
                            .'|/cours(?'
                                .'|(*:1342)'
                                .'|/(?'
                                    .'|category/([^/]++)(*:1372)'
                                    .'|all\\-categories(*:1396)'
                                .')'
                            .')'
                        .')'
                        .'|messagerie/(?'
                            .'|formateur/([^/]++)/(?'
                                .'|apprenant/([^/]++)(?'
                                    .'|(*:1465)'
                                    .'|/envoyer(*:1482)'
                                .')'
                                .'|conversations(*:1505)'
                            .')'
                            .'|apprenant/([^/]++)/(?'
                                .'|conversations(*:1550)'
                                .'|formateur/([^/]++)/envoyer(*:1585)'
                            .')'
                            .'|([^/]++)/marquer\\-lu(*:1615)'
                            .'|apprenant/([^/]++)/formateurs(*:1653)'
                            .'|formateur/([^/]++)/apprenants(*:1691)'
                        .')'
                        .'|notification/([^/]++)(?'
                            .'|/read(*:1730)'
                            .'|(*:1739)'
                        .')'
                        .'|progression/(?'
                            .'|([^/]++)(*:1772)'
                            .'|apprenant/([^/]++)(?'
                                .'|(*:1802)'
                                .'|/cours/([^/]++)(*:1826)'
                            .')'
                        .')'
                        .'|quiz/(?'
                            .'|([^/]++)(?'
                                .'|(*:1856)'
                                .'|(*:1865)'
                            .')'
                            .'|by\\-idmodule/([^/]++)(*:1896)'
                            .'|competence/([^/]++)/([^/]++)(?'
                                .'|(*:1936)'
                            .')'
                            .'|action/([^/]++)/([^/]++)/([^/]++)/([^/]++)(?'
                                .'|(*:1991)'
                            .')'
                            .'|sous\\-competence\\-by\\-id/([^/]++)/([^/]++)/([^/]++)(*:2052)'
                            .'|quiz\\-action(?'
                                .'|\\-by\\-id/([^/]++)(?'
                                    .'|(*:2096)'
                                .')'
                                .'|/([^/]++)/([^/]++)/([^/]++)(?'
                                    .'|(*:2136)'
                                .')'
                            .')'
                        .')'
                        .'|sous\\-competence/([^/]++)(?'
                            .'|(*:2176)'
                        .')'
                    .')'
                .')'
                .'|/_(?'
                    .'|error/(\\d+)(?:\\.([^/]++))?(*:2219)'
                    .'|wdt/([^/]++)(*:2240)'
                    .'|profiler/(?'
                        .'|font/([^/\\.]++)\\.woff2(*:2283)'
                        .'|([^/]++)(?'
                            .'|/(?'
                                .'|search/results(*:2321)'
                                .'|router(*:2336)'
                                .'|exception(?'
                                    .'|(*:2357)'
                                    .'|\\.css(*:2371)'
                                .')'
                            .')'
                            .'|(*:2382)'
                        .')'
                    .')'
                .')'
            .')/?$}sDu',
    ],
    [ // $dynamicRoutes
        54 => [[['_route' => 'api_evenement_by_administrateur', '_controller' => 'App\\Controller\\EvenementController::getByAdministrateur'], ['id'], ['GET' => 0], null, false, true, null]],
        72 => [[['_route' => 'api_errors', '_controller' => 'api_platform.action.error_page'], ['status'], ['GET' => 0, 'HEAD' => 1], null, false, true, null]],
        108 => [[['_route' => 'api_genid', '_controller' => 'api_platform.action.not_exposed', '_api_respond' => 'true'], ['id'], ['GET' => 0, 'HEAD' => 1], null, false, true, null]],
        142 => [[['_route' => 'api_validation_errors', '_controller' => 'api_platform.action.not_exposed'], ['id'], ['GET' => 0, 'HEAD' => 1], null, false, true, null]],
        179 => [[['_route' => 'api_entrypoint', '_controller' => 'api_platform.action.entrypoint', '_format' => '', '_api_respond' => 'true', 'index' => 'index'], ['index', '_format'], ['GET' => 0, 'HEAD' => 1], null, false, true, null]],
        213 => [[['_route' => 'api_doc', '_controller' => 'api_platform.action.documentation', '_format' => '', '_api_respond' => 'true'], ['_format'], ['GET' => 0, 'HEAD' => 1], null, false, true, null]],
        262 => [[['_route' => 'api_diagnostic_check_apprenant', '_controller' => 'App\\Controller\\DiagnosticController::checkApprenant'], ['id'], ['GET' => 0], null, false, true, null]],
        284 => [[['_route' => 'api_diagnostic_check_cours', '_controller' => 'App\\Controller\\DiagnosticController::checkCours'], ['id'], ['GET' => 0], null, false, true, null]],
        321 => [[['_route' => 'api_diagnostic_check_progression', '_controller' => 'App\\Controller\\DiagnosticController::checkProgression'], ['apprenantId', 'coursId'], ['GET' => 0], null, false, true, null]],
        366 => [[['_route' => 'api_diagnostic_create_progression', '_controller' => 'App\\Controller\\DiagnosticController::createProgression'], ['apprenantId', 'coursId'], ['POST' => 0], null, false, true, null]],
        413 => [[['_route' => 'api_jsonld_context', '_controller' => 'api_platform.jsonld.action.context', '_format' => 'jsonld', '_api_respond' => 'true'], ['shortName', '_format'], ['GET' => 0, 'HEAD' => 1], null, false, true, null]],
        438 => [[['_route' => 'api_competence_show', '_controller' => 'App\\Controller\\CompetenceController::show'], ['id'], ['GET' => 0], null, false, true, null]],
        461 => [
            [['_route' => 'api_cours_show', '_controller' => 'App\\Controller\\CourseController::show'], ['id'], ['GET' => 0], null, false, true, null],
            [['_route' => 'api_cours_update', '_controller' => 'App\\Controller\\CourseController::update'], ['id'], ['PUT' => 0], null, false, true, null],
            [['_route' => 'api_cours_delete', '_controller' => 'App\\Controller\\CourseController::delete'], ['id'], ['DELETE' => 0], null, false, true, null],
        ],
        522 => [[['_route' => 'api_certificat_check_and_generate', '_controller' => 'App\\Controller\\CertificatController::checkAndGenerate'], ['apprenantId', 'coursId'], ['GET' => 0], null, false, true, null]],
        538 => [[['_route' => 'api_certificat_show', '_controller' => 'App\\Controller\\CertificatController::show'], ['id'], ['GET' => 0], null, false, true, null]],
        565 => [[['_route' => 'api_certificat_generate_direct', '_controller' => 'App\\Controller\\CertificatController::generateDirect'], [], ['POST' => 0], null, false, false, null]],
        573 => [[['_route' => 'api_certificat_generate', '_controller' => 'App\\Controller\\CertificatController::generate'], [], ['POST' => 0], null, false, false, null]],
        603 => [[['_route' => 'api_certificat_by_apprenant', '_controller' => 'App\\Controller\\CertificatController::getCertificatsByApprenant'], ['apprenantId'], ['GET' => 0], null, false, true, null]],
        626 => [[['_route' => 'api_certificat_by_apprenant_and_cours', '_controller' => 'App\\Controller\\CertificatController::getCertificatByApprenantAndCours'], ['apprenantId', 'coursId'], ['GET' => 0], null, false, true, null]],
        655 => [[['_route' => 'api_certificat_download', '_controller' => 'App\\Controller\\CertificatController::download'], ['id'], ['GET' => 0], null, false, false, null]],
        666 => [[['_route' => 'api_certificat_data', '_controller' => 'App\\Controller\\CertificatController::getCertificatData'], ['id'], ['GET' => 0], null, false, false, null]],
        706 => [
            [['_route' => '_api_validation_errors_problem', '_controller' => 'api_platform.action.placeholder', '_format' => null, '_stateless' => true, '_api_resource_class' => 'ApiPlatform\\Validator\\Exception\\ValidationException', '_api_operation_name' => '_api_validation_errors_problem'], ['id'], ['GET' => 0], null, false, true, null],
            [['_route' => '_api_validation_errors_hydra', '_controller' => 'api_platform.action.placeholder', '_format' => null, '_stateless' => true, '_api_resource_class' => 'ApiPlatform\\Validator\\Exception\\ValidationException', '_api_operation_name' => '_api_validation_errors_hydra'], ['id'], ['GET' => 0], null, false, true, null],
            [['_route' => '_api_validation_errors_jsonapi', '_controller' => 'api_platform.action.placeholder', '_format' => null, '_stateless' => true, '_api_resource_class' => 'ApiPlatform\\Validator\\Exception\\ValidationException', '_api_operation_name' => '_api_validation_errors_jsonapi'], ['id'], ['GET' => 0], null, false, true, null],
        ],
        737 => [
            [['_route' => 'api_action_show', '_controller' => 'App\\Controller\\ActionController::show'], ['id'], ['GET' => 0], null, false, true, null],
            [['_route' => 'api_actions_update', '_controller' => 'App\\Controller\\ActionController::update'], ['id'], ['PUT' => 0], null, false, true, null],
            [['_route' => 'api_actions_delete', '_controller' => 'App\\Controller\\ActionController::delete'], ['id'], ['DELETE' => 0], null, false, true, null],
        ],
        779 => [[['_route' => 'api_admin_user_approve', '_controller' => 'App\\Controller\\AdminController::approveUser'], ['id'], ['POST' => 0, 'OPTIONS' => 1], null, false, true, null]],
        802 => [[['_route' => 'api_admin_user_reject', '_controller' => 'App\\Controller\\AdminController::rejectUser'], ['id'], ['POST' => 0, 'OPTIONS' => 1], null, false, true, null]],
        823 => [[['_route' => 'api_admin_user_edit', '_controller' => 'App\\Controller\\AdminController::editUser'], ['id'], ['PUT' => 0, 'OPTIONS' => 1], null, false, true, null]],
        846 => [[['_route' => 'api_admin_user_delete', '_controller' => 'App\\Controller\\AdminController::deleteUser'], ['id'], ['DELETE' => 0, 'OPTIONS' => 1], null, false, true, null]],
        883 => [[['_route' => 'api_admin_apprenant_cours', '_controller' => 'App\\Controller\\AdminController::getApprenantCours'], ['id'], ['GET' => 0], null, false, false, null]],
        903 => [
            [['_route' => 'api_admin_apprenant_assign_cours', '_controller' => 'App\\Controller\\AdminController::assignCourseToApprenant'], ['apprenantId', 'coursId'], ['POST' => 0], null, false, true, null],
            [['_route' => 'api_admin_apprenant_remove_cours', '_controller' => 'App\\Controller\\AdminController::removeCourseFromApprenant'], ['apprenantId', 'coursId'], ['DELETE' => 0], null, false, true, null],
        ],
        931 => [[['_route' => 'api_user_update', '_controller' => 'App\\Controller\\AuthController::updateUser'], ['id'], ['PUT' => 0, 'OPTIONS' => 1], null, false, true, null]],
        946 => [[['_route' => 'api_user_delete', '_controller' => 'App\\Controller\\AuthController::deleteUser'], ['id'], ['DELETE' => 0, 'OPTIONS' => 1], null, false, false, null]],
        982 => [[['_route' => 'api_reset_password', '_controller' => 'App\\Controller\\AuthController::resetPassword'], ['token'], ['POST' => 0, 'OPTIONS' => 1], null, false, true, null]],
        1011 => [[['_route' => 'api_reclamation_show', '_controller' => 'App\\Controller\\ReclamationController::show'], ['id'], ['GET' => 0], null, false, true, null]],
        1026 => [[['_route' => 'api_reclamation_reply', '_controller' => 'App\\Controller\\ReclamationController::reply'], ['id'], ['POST' => 0], null, false, false, null]],
        1065 => [[['_route' => 'api_evaluation_show', '_controller' => 'App\\Controller\\EvaluationController::show'], ['id'], ['GET' => 0], null, false, true, null]],
        1094 => [[['_route' => 'api_evaluation_by_idmodule', '_controller' => 'App\\Controller\\EvaluationController::getEvaluationsByIdmodule'], ['idmodule'], ['GET' => 0], null, false, true, null]],
        1122 => [[['_route' => 'api_evaluation_by_idmodule_apprenant', '_controller' => 'App\\Controller\\EvaluationController::getEvaluationByIdmoduleAndApprenant'], ['idmodule', 'apprenantId'], ['GET' => 0], null, false, true, null]],
        1164 => [[['_route' => 'api_evaluation_by_quiz_apprenant', '_controller' => 'App\\Controller\\EvaluationController::getEvaluationByQuizAndApprenant'], ['quizId', 'apprenantId'], ['GET' => 0], null, false, true, null]],
        1215 => [[['_route' => 'api_evaluation_detail_by_quiz_apprenant', '_controller' => 'App\\Controller\\EvaluationDetailController::getByQuizAndApprenant'], ['quizId', 'apprenantId'], ['GET' => 0], null, false, true, null]],
        1244 => [
            [['_route' => 'api_evenement_show', '_controller' => 'App\\Controller\\EvenementController::show'], ['id'], ['GET' => 0], null, false, true, null],
            [['_route' => 'api_evenement_update', '_controller' => 'App\\Controller\\EvenementController::update'], ['id'], ['PUT' => 0], null, false, true, null],
            [['_route' => 'api_evenement_delete', '_controller' => 'App\\Controller\\EvenementController::delete'], ['id'], ['DELETE' => 0], null, false, true, null],
        ],
        1280 => [
            [['_route' => 'api_evenement_add_administrateur', '_controller' => 'App\\Controller\\EvenementController::addAdministrateur'], ['id', 'adminId'], ['POST' => 0], null, false, true, null],
            [['_route' => 'api_evenement_remove_administrateur', '_controller' => 'App\\Controller\\EvenementController::removeAdministrateur'], ['id', 'adminId'], ['DELETE' => 0], null, false, true, null],
        ],
        1324 => [[['_route' => 'api_formateur_apprenant', '_controller' => 'App\\Controller\\FormateurController::getApprenant'], ['id'], ['GET' => 0], null, false, true, null]],
        1342 => [[['_route' => 'api_formateur_apprenant_cours', '_controller' => 'App\\Controller\\FormateurController::getApprenantCours'], ['id'], ['GET' => 0], null, false, false, null]],
        1372 => [[['_route' => 'api_formateur_apprenant_cours_by_category', '_controller' => 'App\\Controller\\FormateurController::getApprenantCoursByCategory'], ['id', 'category'], ['GET' => 0], null, false, true, null]],
        1396 => [[['_route' => 'api_formateur_apprenant_cours_all_categories', '_controller' => 'App\\Controller\\FormateurController::getApprenantCoursAllCategories'], ['id'], ['GET' => 0], null, false, false, null]],
        1465 => [[['_route' => 'api_messagerie_get_conversation', '_controller' => 'App\\Controller\\MessagerieController::getConversation'], ['formateurId', 'apprenantId'], ['GET' => 0], null, false, true, null]],
        1482 => [[['_route' => 'api_messagerie_formateur_envoyer', '_controller' => 'App\\Controller\\MessagerieController::formateurEnvoyerMessage'], ['formateurId', 'apprenantId'], ['POST' => 0], null, false, false, null]],
        1505 => [[['_route' => 'api_messagerie_formateur_conversations', '_controller' => 'App\\Controller\\MessagerieController::getFormateurConversations'], ['formateurId'], ['GET' => 0], null, false, false, null]],
        1550 => [[['_route' => 'api_messagerie_apprenant_conversations', '_controller' => 'App\\Controller\\MessagerieController::getApprenantConversations'], ['apprenantId'], ['GET' => 0], null, false, false, null]],
        1585 => [[['_route' => 'api_messagerie_apprenant_envoyer', '_controller' => 'App\\Controller\\MessagerieController::apprenantEnvoyerMessage'], ['apprenantId', 'formateurId'], ['POST' => 0], null, false, false, null]],
        1615 => [[['_route' => 'api_messagerie_marquer_lu', '_controller' => 'App\\Controller\\MessagerieController::marquerLu'], ['id'], ['PUT' => 0], null, false, false, null]],
        1653 => [[['_route' => 'api_messagerie_apprenant_formateurs', '_controller' => 'App\\Controller\\MessagerieController::getFormateursForApprenant'], ['apprenantId'], ['GET' => 0], null, false, false, null]],
        1691 => [[['_route' => 'api_messagerie_formateur_apprenants', '_controller' => 'App\\Controller\\MessagerieController::getApprenantsForFormateur'], ['formateurId'], ['GET' => 0], null, false, false, null]],
        1730 => [[['_route' => 'api_notification_mark_read', '_controller' => 'App\\Controller\\NotificationController::markAsRead'], ['id'], ['PUT' => 0], null, false, false, null]],
        1739 => [[['_route' => 'api_notification_delete', '_controller' => 'App\\Controller\\NotificationController::deleteNotification'], ['id'], ['DELETE' => 0], null, false, true, null]],
        1772 => [[['_route' => 'api_progression_show', '_controller' => 'App\\Controller\\ProgressionController::show'], ['id'], ['GET' => 0], null, false, true, null]],
        1802 => [[['_route' => 'api_progression_by_apprenant', '_controller' => 'App\\Controller\\ProgressionController::getProgressionByApprenant'], ['apprenantId'], ['GET' => 0], null, false, true, null]],
        1826 => [[['_route' => 'api_progression_by_apprenant_cours', '_controller' => 'App\\Controller\\ProgressionController::getProgressionByApprenantAndCours'], ['apprenantId', 'coursId'], ['GET' => 0], null, false, true, null]],
        1856 => [
            [['_route' => 'api_quiz_show', '_controller' => 'App\\Controller\\QuizController::show'], ['IDModule'], ['GET' => 0], null, false, true, null],
            [['_route' => 'api_quiz_update', '_controller' => 'App\\Controller\\QuizController::update'], ['IDModule'], ['PUT' => 0], null, false, true, null],
        ],
        1865 => [[['_route' => 'api_quiz_delete', '_controller' => 'App\\Controller\\QuizController::delete'], ['id'], ['DELETE' => 0], null, false, true, null]],
        1896 => [[['_route' => 'api_quiz_delete_by_idmodule', '_controller' => 'App\\Controller\\QuizController::deleteByIdModule'], ['IDModule'], ['DELETE' => 0], null, false, true, null]],
        1936 => [
            [['_route' => 'api_competence_delete', '_controller' => 'App\\Controller\\QuizController::deleteCompetence'], ['IDModule', 'competenceId'], ['DELETE' => 0], null, false, true, null],
            [['_route' => 'api_competence_update', '_controller' => 'App\\Controller\\QuizController::updateCompetence'], ['IDModule', 'competenceId'], ['PUT' => 0], null, false, true, null],
        ],
        1991 => [
            [['_route' => 'api_quiz_action_delete_legacy', '_controller' => 'App\\Controller\\QuizController::deleteAction'], ['IDModule', 'competenceId', 'actionNomFR', 'actionNomEN'], ['DELETE' => 0], null, false, true, null],
            [['_route' => 'api_quiz_action_update_legacy', '_controller' => 'App\\Controller\\QuizController::updateAction'], ['IDModule', 'competenceId', 'actionNomFR', 'actionNomEN'], ['PUT' => 0], null, false, true, null],
        ],
        2052 => [[['_route' => 'api_sous_competence_update_by_id', '_controller' => 'App\\Controller\\QuizController::updateSousCompetenceById'], ['IDModule', 'competenceId', 'id'], ['PUT' => 0], null, false, true, null]],
        2096 => [
            [['_route' => 'api_quiz_action_delete_by_id', '_controller' => 'App\\Controller\\QuizController::deleteQuizActionById'], ['id'], ['DELETE' => 0], null, false, true, null],
            [['_route' => 'api_quiz_action_update_by_id', '_controller' => 'App\\Controller\\QuizController::updateQuizActionById'], ['id'], ['PUT' => 0], null, false, true, null],
        ],
        2136 => [
            [['_route' => 'api_quiz_action_delete', '_controller' => 'App\\Controller\\QuizController::deleteQuizAction'], ['IDModule', 'actionNomFR', 'actionNomEN'], ['DELETE' => 0], null, false, true, null],
            [['_route' => 'api_quiz_action_update', '_controller' => 'App\\Controller\\QuizController::updateQuizAction'], ['IDModule', 'actionNomFR', 'actionNomEN'], ['PUT' => 0], null, false, true, null],
        ],
        2176 => [
            [['_route' => 'api_sous_competence_show', '_controller' => 'App\\Controller\\SousCompetenceController::show'], ['id'], ['GET' => 0], null, false, true, null],
            [['_route' => 'api_sous_competence_update', '_controller' => 'App\\Controller\\SousCompetenceController::update'], ['id'], ['PUT' => 0], null, false, true, null],
            [['_route' => 'api_sous_competence_delete', '_controller' => 'App\\Controller\\SousCompetenceController::delete'], ['id'], ['DELETE' => 0], null, false, true, null],
        ],
        2219 => [[['_route' => '_preview_error', '_controller' => 'error_controller::preview', '_format' => 'html'], ['code', '_format'], null, null, false, true, null]],
        2240 => [[['_route' => '_wdt', '_controller' => 'web_profiler.controller.profiler::toolbarAction'], ['token'], null, null, false, true, null]],
        2283 => [[['_route' => '_profiler_font', '_controller' => 'web_profiler.controller.profiler::fontAction'], ['fontName'], null, null, false, false, null]],
        2321 => [[['_route' => '_profiler_search_results', '_controller' => 'web_profiler.controller.profiler::searchResultsAction'], ['token'], null, null, false, false, null]],
        2336 => [[['_route' => '_profiler_router', '_controller' => 'web_profiler.controller.router::panelAction'], ['token'], null, null, false, false, null]],
        2357 => [[['_route' => '_profiler_exception', '_controller' => 'web_profiler.controller.exception_panel::body'], ['token'], null, null, false, false, null]],
        2371 => [[['_route' => '_profiler_exception_css', '_controller' => 'web_profiler.controller.exception_panel::stylesheet'], ['token'], null, null, false, false, null]],
        2382 => [
            [['_route' => '_profiler', '_controller' => 'web_profiler.controller.profiler::panelAction'], ['token'], null, null, false, true, null],
            [null, null, null, null, false, false, 0],
        ],
    ],
    null, // $checkCondition
];
