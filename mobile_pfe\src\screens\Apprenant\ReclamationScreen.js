import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Modal,
  ScrollView,
  Alert,
  RefreshControl
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { AuthContext } from '../../contexts/AuthContext';
import { ThemeContext } from '../../contexts/ThemeContext';
import reclamationService from '../../services/reclamationService';
import LoadingScreen from '../../components/LoadingScreen';

const ReclamationItem = ({ reclamation, onPress }) => {
  const { theme } = useContext(ThemeContext);

  // Function to get status color
  const getStatusColor = () => {
    switch (reclamation.status) {
      case 'pending': return theme.warning;
      case 'in-progress': return theme.primary;
      case 'resolved': return theme.success;
      case 'closed': return theme.text.tertiary;
      default: return theme.warning;
    }
  };

  // Function to get status text
  const getStatusText = () => {
    switch (reclamation.status) {
      case 'pending': return 'En attente';
      case 'in-progress': return 'En cours';
      case 'resolved': return 'Résolu';
      case 'closed': return 'Fermé';
      default: return 'En attente';
    }
  };

  return (
    <TouchableOpacity
      style={[styles.reclamationItem, { backgroundColor: theme.card, borderColor: theme.border }]}
      onPress={() => onPress(reclamation)}
    >
      <View style={styles.reclamationHeader}>
        <Text style={[styles.reclamationTitle, { color: theme.text.primary }]} numberOfLines={1}>
          {reclamation.title}
        </Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor() + '20' }]}>
          <Text style={[styles.statusText, { color: getStatusColor() }]}>
            {getStatusText()}
          </Text>
        </View>
      </View>

      <Text style={[styles.reclamationDescription, { color: theme.text.secondary }]} numberOfLines={2}>
        {reclamation.description}
      </Text>

      <View style={styles.reclamationFooter}>
        <Text style={[styles.reclamationDate, { color: theme.text.tertiary }]}>
          {reclamation.createdAt}
        </Text>

        {reclamation.replies > 0 && (
          <View style={styles.repliesContainer}>
            <MaterialCommunityIcons name="message-reply" size={14} color={theme.text.tertiary} />
            <Text style={[styles.repliesText, { color: theme.text.tertiary }]}>{reclamation.replies} réponses</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const ReclamationScreen = ({ navigation }) => {
  const { user } = useContext(AuthContext);
  const { theme, isDarkMode } = useContext(ThemeContext);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [reclamations, setReclamations] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [newReclamation, setNewReclamation] = useState({
    title: '',
    description: '',
  });
  const [error, setError] = useState(null);

  const fetchReclamations = async () => {
    try {
      setError(null);
      const reclamationsData = await reclamationService.getUserReclamations();
      setReclamations(reclamationsData);
    } catch (err) {
      console.error('Error fetching reclamations:', err);
      setError('Impossible de charger les réclamations');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchReclamations();
    setRefreshing(false);
  };

  const handleReclamationPress = (reclamation) => {
    navigation.navigate('ReclamationDetail', {
      reclamationId: reclamation.id,
      title: reclamation.title,
    });
  };

  const handleCreateReclamation = async () => {
    if (!newReclamation.title.trim() || !newReclamation.description.trim()) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs');
      return;
    }

    try {
      setLoading(true);
      await reclamationService.createReclamation({
        title: newReclamation.title.trim(),
        description: newReclamation.description.trim(),
      });

      // Reset form and close modal
      setNewReclamation({ title: '', description: '' });
      setModalVisible(false);

      // Refresh reclamations list
      await fetchReclamations();

      Alert.alert('Succès', 'Réclamation créée avec succès');
    } catch (err) {
      console.error('Error creating reclamation:', err);
      Alert.alert('Erreur', 'Impossible de créer la réclamation');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const loadReclamations = async () => {
      setLoading(true);
      await fetchReclamations();
      setLoading(false);
    };

    loadReclamations();
  }, []);

  if (loading) {
    return <LoadingScreen message="Chargement des réclamations..." />;
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={[styles.header, { backgroundColor: theme.card, borderBottomColor: theme.border }]}>
        <Text style={[styles.headerTitle, { color: theme.text.primary }]}>Mes Réclamations</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.primary }]}
          onPress={() => setModalVisible(true)}
        >
          <MaterialCommunityIcons name="plus" size={24} color={theme.text.inverse} />
        </TouchableOpacity>
      </View>

      {error ? (
        <View style={styles.errorContainer}>
          <MaterialCommunityIcons name="alert-circle" size={48} color={theme.danger} />
          <Text style={[styles.errorText, { color: theme.text.primary }]}>{error}</Text>
          <TouchableOpacity style={[styles.retryButton, { backgroundColor: theme.primary }]} onPress={onRefresh}>
            <Text style={[styles.retryButtonText, { color: theme.text.inverse }]}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={reclamations}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <ReclamationItem
              reclamation={item}
              onPress={handleReclamationPress}
            />
          )}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <MaterialCommunityIcons name="alert-circle" size={48} color={theme.text.tertiary} />
              <Text style={[styles.emptyText, { color: theme.text.primary }]}>
                Aucune réclamation
              </Text>
              <Text style={[styles.emptySubtext, { color: theme.text.secondary }]}>
                Créez une nouvelle réclamation en appuyant sur le bouton +
              </Text>
            </View>
          }
        />
      )}

      {/* Create Reclamation Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={[styles.modalContent, { backgroundColor: theme.card }]}>
            <View style={[styles.modalHeader, { borderBottomColor: theme.border }]}>
              <Text style={[styles.modalTitle, { color: theme.text.primary }]}>Nouvelle Réclamation</Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <MaterialCommunityIcons name="close" size={24} color={theme.text.secondary} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              <Text style={[styles.inputLabel, { color: theme.text.primary }]}>Titre</Text>
              <TextInput
                style={[styles.input, {
                  backgroundColor: isDarkMode ? theme.background : '#F3F4F6',
                  borderColor: theme.border,
                  color: theme.text.primary
                }]}
                placeholder="Titre de la réclamation"
                placeholderTextColor={theme.text.tertiary}
                value={newReclamation.title}
                onChangeText={(text) => setNewReclamation({ ...newReclamation, title: text })}
              />

              <Text style={[styles.inputLabel, { color: theme.text.primary }]}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea, {
                  backgroundColor: isDarkMode ? theme.background : '#F3F4F6',
                  borderColor: theme.border,
                  color: theme.text.primary
                }]}
                placeholder="Décrivez votre problème en détail..."
                placeholderTextColor={theme.text.tertiary}
                value={newReclamation.description}
                onChangeText={(text) => setNewReclamation({ ...newReclamation, description: text })}
                multiline
                numberOfLines={6}
                textAlignVertical="top"
              />
            </ScrollView>

            <View style={[styles.modalFooter, { borderTopColor: theme.border }]}>
              <TouchableOpacity
                style={[styles.cancelButton, { backgroundColor: isDarkMode ? theme.background : '#F3F4F6' }]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={[styles.cancelButtonText, { color: theme.text.secondary }]}>Annuler</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.submitButton, { backgroundColor: theme.primary }]}
                onPress={handleCreateReclamation}
              >
                <Text style={[styles.submitButtonText, { color: theme.text.inverse }]}>Soumettre</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
  },
  reclamationItem: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
  },
  reclamationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reclamationTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  reclamationDescription: {
    fontSize: 14,
    marginBottom: 12,
  },
  reclamationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reclamationDate: {
    fontSize: 12,
  },
  repliesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  repliesText: {
    fontSize: 12,
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    marginTop: 48,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    borderRadius: 12,
    overflow: 'hidden',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 16,
    maxHeight: 400,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
  },
  textArea: {
    height: 120,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    gap: 12,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  submitButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  submitButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default ReclamationScreen;