import { COLORS } from './constants';

/**
 * Format date to a readable string
 * @param {string|Date} date - Date to format
 * @param {string} format - Format to use (default: 'dd/mm/yyyy')
 * @returns {string} Formatted date
 */
export const formatDate = (date, format = 'dd/mm/yyyy') => {
  if (!date) return '';
  
  const d = new Date(date);
  
  if (isNaN(d.getTime())) return '';
  
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  
  switch (format) {
    case 'dd/mm/yyyy':
      return `${day}/${month}/${year}`;
    case 'mm/dd/yyyy':
      return `${month}/${day}/${year}`;
    case 'yyyy-mm-dd':
      return `${year}-${month}-${day}`;
    case 'dd/mm/yyyy hh:mm':
      return `${day}/${month}/${year} ${hours}:${minutes}`;
    case 'relative':
      return getRelativeTime(d);
    default:
      return `${day}/${month}/${year}`;
  }
};

/**
 * Get relative time from date
 * @param {Date} date - Date to get relative time from
 * @returns {string} Relative time
 */
export const getRelativeTime = (date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const months = Math.floor(days / 30);
  const years = Math.floor(months / 12);
  
  if (seconds < 60) {
    return 'À l\'instant';
  } else if (minutes < 60) {
    return `Il y a ${minutes} minute${minutes > 1 ? 's' : ''}`;
  } else if (hours < 24) {
    return `Il y a ${hours} heure${hours > 1 ? 's' : ''}`;
  } else if (days < 30) {
    return `Il y a ${days} jour${days > 1 ? 's' : ''}`;
  } else if (months < 12) {
    return `Il y a ${months} mois`;
  } else {
    return `Il y a ${years} an${years > 1 ? 's' : ''}`;
  }
};

/**
 * Truncate text to a certain length
 * @param {string} text - Text to truncate
 * @param {number} length - Maximum length
 * @returns {string} Truncated text
 */
export const truncateText = (text, length = 100) => {
  if (!text) return '';
  
  if (text.length <= length) return text;
  
  return text.substring(0, length) + '...';
};

/**
 * Get color for course progress
 * @param {number} progress - Progress percentage (0-100)
 * @returns {string} Color
 */
export const getProgressColor = (progress) => {
  if (progress >= 75) {
    return COLORS.secondary;
  } else if (progress >= 50) {
    return COLORS.primary;
  } else if (progress >= 25) {
    return COLORS.warning;
  } else {
    return COLORS.error;
  }
};

/**
 * Format file size
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Get file extension from filename
 * @param {string} filename - Filename
 * @returns {string} File extension
 */
export const getFileExtension = (filename) => {
  if (!filename) return '';
  
  return filename.split('.').pop().toLowerCase();
};

/**
 * Get icon name for file type
 * @param {string} filename - Filename
 * @returns {string} Icon name
 */
export const getFileIcon = (filename) => {
  const extension = getFileExtension(filename);
  
  switch (extension) {
    case 'pdf':
      return 'file-pdf-box';
    case 'doc':
    case 'docx':
      return 'file-word-box';
    case 'xls':
    case 'xlsx':
      return 'file-excel-box';
    case 'ppt':
    case 'pptx':
      return 'file-powerpoint-box';
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return 'file-image-box';
    case 'mp3':
    case 'wav':
    case 'ogg':
      return 'file-music-box';
    case 'mp4':
    case 'avi':
    case 'mov':
      return 'file-video-box';
    case 'zip':
    case 'rar':
    case '7z':
      return 'file-zip-box';
    default:
      return 'file-document-box';
  }
};
