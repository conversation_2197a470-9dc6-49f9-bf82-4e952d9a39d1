import React, { useState, useEffect, useContext, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { AuthContext } from '../../contexts/AuthContext';
import { ThemeContext } from '../../contexts/ThemeContext';
import reclamationService from '../../services/reclamationService';
import LoadingScreen from '../../components/LoadingScreen';

const ReplyItem = ({ reply, isAdmin }) => {
  const { theme, isDarkMode } = useContext(ThemeContext);

  return (
    <View style={[
      styles.replyContainer,
      isAdmin
        ? [styles.adminReply, {
            backgroundColor: isDarkMode ? `${theme.primary}20` : '#EEF2FF',
            borderColor: isDarkMode ? `${theme.primary}30` : '#E0E7FF'
          }]
        : [styles.userReply, {
            backgroundColor: isDarkMode ? `${theme.success}20` : '#F0FDF4',
            borderColor: isDarkMode ? `${theme.success}30` : '#DCFCE7'
          }]
    ]}>
      <View style={styles.replyHeader}>
        <Text style={[styles.replyAuthor, { color: theme.text.primary }]}>
          {isAdmin ? 'Administrateur' : 'Vous'}
        </Text>
        <Text style={[styles.replyDate, { color: theme.text.tertiary }]}>{reply.createdAt}</Text>
      </View>
      <Text style={[styles.replyContent, { color: theme.text.primary }]}>{reply.content}</Text>
    </View>
  );
};

const ReclamationDetailScreen = ({ route, navigation }) => {
  const { reclamationId } = route.params;
  const { user } = useContext(AuthContext);
  const { theme, isDarkMode } = useContext(ThemeContext);
  const [loading, setLoading] = useState(true);
  const [reclamation, setReclamation] = useState(null);
  const [replyContent, setReplyContent] = useState('');
  const [sendingReply, setSendingReply] = useState(false);
  const [error, setError] = useState(null);

  const scrollViewRef = useRef();

  const fetchReclamationDetails = async () => {
    try {
      setError(null);
      const reclamationData = await reclamationService.getReclamationDetails(reclamationId);
      setReclamation(reclamationData);

      // Update navigation title
      navigation.setOptions({
        title: reclamationData.title || 'Détails de la réclamation',
        headerStyle: {
          backgroundColor: theme.card,
        },
        headerTintColor: theme.text.primary,
      });
    } catch (err) {
      console.error('Error fetching reclamation details:', err);
      setError('Impossible de charger les détails de la réclamation');
    }
  };

  const handleSendReply = async () => {
    if (!replyContent.trim()) {
      Alert.alert('Erreur', 'Veuillez entrer un message');
      return;
    }

    try {
      setSendingReply(true);
      await reclamationService.replyToReclamation(reclamationId, replyContent.trim());

      // Clear input
      setReplyContent('');

      // Refresh reclamation details
      await fetchReclamationDetails();

      // Scroll to bottom
      if (scrollViewRef.current) {
        setTimeout(() => {
          scrollViewRef.current.scrollToEnd({ animated: true });
        }, 100);
      }
    } catch (err) {
      console.error('Error sending reply:', err);
      Alert.alert('Erreur', 'Impossible d\'envoyer la réponse');
    } finally {
      setSendingReply(false);
    }
  };

  useEffect(() => {
    const loadReclamationDetails = async () => {
      setLoading(true);
      await fetchReclamationDetails();
      setLoading(false);
    };

    loadReclamationDetails();
  }, [reclamationId]);

  // Function to get status color
  const getStatusColor = () => {
    if (!reclamation) return theme.warning;

    switch (reclamation.status) {
      case 'pending': return theme.warning;
      case 'in-progress': return theme.primary;
      case 'resolved': return theme.success;
      case 'closed': return theme.text.tertiary;
      default: return theme.warning;
    }
  };

  // Function to get status text
  const getStatusText = () => {
    if (!reclamation) return 'En attente';

    switch (reclamation.status) {
      case 'pending': return 'En attente';
      case 'in-progress': return 'En cours';
      case 'resolved': return 'Résolu';
      case 'closed': return 'Fermé';
      default: return 'En attente';
    }
  };

  if (loading) {
    return <LoadingScreen message="Chargement des détails..." />;
  }

  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.background }]}>
        <MaterialCommunityIcons name="alert-circle" size={48} color={theme.danger} />
        <Text style={[styles.errorText, { color: theme.text.primary }]}>{error}</Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: theme.primary }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.retryButtonText, { color: theme.text.inverse }]}>Retour</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!reclamation) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.background }]}>
        <MaterialCommunityIcons name="alert-circle" size={48} color={theme.danger} />
        <Text style={[styles.errorText, { color: theme.text.primary }]}>Réclamation non trouvée</Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: theme.primary }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.retryButtonText, { color: theme.text.inverse }]}>Retour</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[styles.container, { backgroundColor: theme.background }]}
      keyboardVerticalOffset={80}
    >
      {/* Reclamation Header */}
      <View style={[styles.reclamationHeader, { backgroundColor: theme.card, borderBottomColor: theme.border }]}>
        <Text style={[styles.reclamationTitle, { color: theme.text.primary }]}>{reclamation.title}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor() + '20' }]}>
          <Text style={[styles.statusText, { color: getStatusColor() }]}>
            {getStatusText()}
          </Text>
        </View>
      </View>

      {/* Reclamation Content */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Reclamation Details */}
        <View style={[styles.detailsContainer, { backgroundColor: theme.card, borderBottomColor: theme.border }]}>
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Date de création:</Text>
            <Text style={[styles.detailValue, { color: theme.text.primary }]}>{reclamation.createdAt}</Text>
          </View>

          {reclamation.updatedAt && (
            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Dernière mise à jour:</Text>
              <Text style={[styles.detailValue, { color: theme.text.primary }]}>{reclamation.updatedAt}</Text>
            </View>
          )}

          <View style={styles.descriptionContainer}>
            <Text style={[styles.descriptionLabel, { color: theme.text.secondary }]}>Description:</Text>
            <Text style={[styles.descriptionText, { color: theme.text.primary }]}>{reclamation.description}</Text>
          </View>
        </View>

        {/* Replies */}
        <View style={[styles.repliesContainer, { backgroundColor: theme.card }]}>
          <Text style={[styles.repliesTitle, { color: theme.text.primary }]}>Réponses</Text>

          {reclamation.replies && reclamation.replies.length > 0 ? (
            reclamation.replies.map((reply, index) => (
              <ReplyItem
                key={index}
                reply={reply}
                isAdmin={reply.isAdmin}
              />
            ))
          ) : (
            <View style={styles.emptyRepliesContainer}>
              <MaterialCommunityIcons name="message-text" size={36} color={theme.text.tertiary} />
              <Text style={[styles.emptyRepliesText, { color: theme.text.secondary }]}>
                Aucune réponse pour le moment
              </Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Reply Input */}
      {reclamation.status !== 'closed' && reclamation.status !== 'resolved' && (
        <View style={[styles.replyInputContainer, {
          backgroundColor: theme.card,
          borderTopColor: theme.border
        }]}>
          <TextInput
            style={[styles.replyInput, {
              backgroundColor: isDarkMode ? theme.background : '#F3F4F6',
              color: theme.text.primary,
              borderColor: theme.border
            }]}
            placeholder="Écrivez votre réponse..."
            placeholderTextColor={theme.text.tertiary}
            value={replyContent}
            onChangeText={setReplyContent}
            multiline
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              { backgroundColor: theme.primary },
              (!replyContent.trim() || sendingReply) && { opacity: 0.7 }
            ]}
            onPress={handleSendReply}
            disabled={!replyContent.trim() || sendingReply}
          >
            {sendingReply ? (
              <ActivityIndicator size="small" color={theme.text.inverse} />
            ) : (
              <MaterialCommunityIcons name="send" size={20} color={theme.text.inverse} />
            )}
          </TouchableOpacity>
        </View>
      )}
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 16,
  },
  reclamationHeader: {
    padding: 16,
    borderBottomWidth: 1,
  },
  reclamationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  detailsContainer: {
    padding: 16,
    marginBottom: 16,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '500',
    width: 140,
  },
  detailValue: {
    fontSize: 14,
    flex: 1,
  },
  descriptionContainer: {
    marginTop: 8,
  },
  descriptionLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
  },
  repliesContainer: {
    padding: 16,
    marginBottom: 16,
  },
  repliesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  replyContainer: {
    padding: 12,
    borderRadius: 12,
    marginBottom: 12,
  },
  adminReply: {
    borderWidth: 1,
  },
  userReply: {
    borderWidth: 1,
  },
  replyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  replyAuthor: {
    fontSize: 14,
    fontWeight: '600',
  },
  replyDate: {
    fontSize: 12,
  },
  replyContent: {
    fontSize: 14,
    lineHeight: 20,
  },
  emptyRepliesContainer: {
    alignItems: 'center',
    padding: 24,
  },
  emptyRepliesText: {
    fontSize: 14,
    marginTop: 8,
  },
  replyInputContainer: {
    flexDirection: 'row',
    padding: 12,
    borderTopWidth: 1,
  },
  replyInput: {
    flex: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    maxHeight: 100,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  sendButtonDisabled: {
    opacity: 0.7,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default ReclamationDetailScreen;
