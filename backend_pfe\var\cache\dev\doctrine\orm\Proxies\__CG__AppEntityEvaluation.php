<?php

namespace Proxies\__CG__\App\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Evaluation extends \App\Entity\Evaluation implements \Doctrine\ORM\Proxy\InternalProxy
{
    use \Symfony\Component\VarExporter\LazyGhostTrait {
        initializeLazyObject as private;
        setLazyObjectAsInitialized as public __setInitialized;
        isLazyObjectInitialized as private;
        createLazyGhost as private;
        resetLazyObject as private;
    }

    public function __load(): void
    {
        $this->initializeLazyObject();
    }
    

    private const LAZY_OBJECT_PROPERTY_SCOPES = [
        "\0".parent::class."\0".'StatutEvaluation' => [parent::class, 'StatutEvaluation', null, 16],
        "\0".parent::class."\0".'apprenant' => [parent::class, 'apprenant', null, 16],
        "\0".parent::class."\0".'createdAt' => [parent::class, 'createdAt', null, 16],
        "\0".parent::class."\0".'evaluationDetails' => [parent::class, 'evaluationDetails', null, 16],
        "\0".parent::class."\0".'formateur' => [parent::class, 'formateur', null, 16],
        "\0".parent::class."\0".'id' => [parent::class, 'id', null, 16],
        "\0".parent::class."\0".'idmodule' => [parent::class, 'idmodule', null, 16],
        "\0".parent::class."\0".'notifications' => [parent::class, 'notifications', null, 16],
        "\0".parent::class."\0".'progressions' => [parent::class, 'progressions', null, 16],
        "\0".parent::class."\0".'quiz' => [parent::class, 'quiz', null, 16],
        'StatutEvaluation' => [parent::class, 'StatutEvaluation', null, 16],
        'apprenant' => [parent::class, 'apprenant', null, 16],
        'createdAt' => [parent::class, 'createdAt', null, 16],
        'evaluationDetails' => [parent::class, 'evaluationDetails', null, 16],
        'formateur' => [parent::class, 'formateur', null, 16],
        'id' => [parent::class, 'id', null, 16],
        'idmodule' => [parent::class, 'idmodule', null, 16],
        'notifications' => [parent::class, 'notifications', null, 16],
        'progressions' => [parent::class, 'progressions', null, 16],
        'quiz' => [parent::class, 'quiz', null, 16],
    ];

    public function __isInitialized(): bool
    {
        return isset($this->lazyObjectState) && $this->isLazyObjectInitialized();
    }

    public function __serialize(): array
    {
        $properties = (array) $this;
        unset($properties["\0" . self::class . "\0lazyObjectState"]);

        return $properties;
    }
}
