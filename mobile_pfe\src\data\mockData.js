// Mock data for the mobile app
// This file provides mock data to use while backend connections are disabled

// Mock user data
export const mockUser = {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  role: 'apprenant',
  phone: '+216 55 123 456',
  profileImage: null,
  createdAt: '2023-05-15',
};

// Mock courses data
export const mockCourses = [
  {
    id: 1,
    title: 'Introduction à la pharmacologie',
    description: 'Ce cours couvre les principes fondamentaux de la pharmacologie, y compris la pharmacocinétique et la pharmacodynamique.',
    image: 'https://images.unsplash.com/photo-1607619056574-7b8d3ee536b2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80',
    category: 'Pharmacologie',
    progress: 75,
    duration: '10h',
    modules: [
      {
        id: 1,
        title: 'Introduction et principes de base',
        completed: true,
        content: 'Contenu du module 1...',
        duration: '2h',
      },
      {
        id: 2,
        title: 'Pharmacocinétique',
        completed: true,
        content: 'Contenu du module 2...',
        duration: '3h',
      },
      {
        id: 3,
        title: 'Pharmacodynamique',
        completed: false,
        content: 'Contenu du module 3...',
        duration: '3h',
      },
      {
        id: 4,
        title: 'Interactions médicamenteuses',
        completed: false,
        content: 'Contenu du module 4...',
        duration: '2h',
      },
    ],
    quizzes: [
      {
        id: 1,
        title: 'Quiz: Principes de base',
        completed: true,
        score: 85,
        questionCount: 10,
        duration: '20 min',
      },
      {
        id: 2,
        title: 'Quiz: Pharmacocinétique',
        completed: true,
        score: 90,
        questionCount: 15,
        duration: '30 min',
      },
      {
        id: 3,
        title: 'Quiz: Pharmacodynamique',
        completed: false,
        questionCount: 12,
        duration: '25 min',
      },
    ],
  },
  {
    id: 2,
    title: 'Pharmacie galénique',
    description: 'Étude des différentes formes pharmaceutiques et de leur préparation.',
    image: 'https://images.unsplash.com/photo-1587854692152-cbe660dbde88?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80',
    category: 'Galénique',
    progress: 30,
    duration: '8h',
    modules: [
      {
        id: 1,
        title: 'Formes pharmaceutiques solides',
        completed: true,
        content: 'Contenu du module 1...',
        duration: '2h',
      },
      {
        id: 2,
        title: 'Formes pharmaceutiques liquides',
        completed: false,
        content: 'Contenu du module 2...',
        duration: '2h',
      },
      {
        id: 3,
        title: 'Formes pharmaceutiques semi-solides',
        completed: false,
        content: 'Contenu du module 3...',
        duration: '2h',
      },
      {
        id: 4,
        title: 'Systèmes thérapeutiques',
        completed: false,
        content: 'Contenu du module 4...',
        duration: '2h',
      },
    ],
    quizzes: [
      {
        id: 1,
        title: 'Quiz: Formes pharmaceutiques solides',
        completed: true,
        score: 80,
        questionCount: 10,
        duration: '20 min',
      },
      {
        id: 2,
        title: 'Quiz: Formes pharmaceutiques liquides',
        completed: false,
        questionCount: 12,
        duration: '25 min',
      },
    ],
  },
  {
    id: 3,
    title: 'Pharmacie clinique',
    description: 'Application des connaissances pharmaceutiques au service du patient.',
    image: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80',
    category: 'Clinique',
    progress: 0,
    duration: '12h',
    modules: [
      {
        id: 1,
        title: 'Introduction à la pharmacie clinique',
        completed: false,
        content: 'Contenu du module 1...',
        duration: '2h',
      },
      {
        id: 2,
        title: 'Analyse pharmaceutique',
        completed: false,
        content: 'Contenu du module 2...',
        duration: '3h',
      },
      {
        id: 3,
        title: 'Suivi thérapeutique',
        completed: false,
        content: 'Contenu du module 3...',
        duration: '4h',
      },
      {
        id: 4,
        title: 'Éducation thérapeutique',
        completed: false,
        content: 'Contenu du module 4...',
        duration: '3h',
      },
    ],
    quizzes: [
      {
        id: 1,
        title: 'Quiz: Introduction à la pharmacie clinique',
        completed: false,
        questionCount: 10,
        duration: '20 min',
      },
    ],
  },
];

// Mock reclamations data
export const mockReclamations = [
  {
    id: 1,
    title: 'Problème d\'accès au quiz',
    description: 'Je n\'arrive pas à accéder au quiz de pharmacocinétique.',
    status: 'pending',
    createdAt: '2023-06-10T14:30:00',
    category: 'technique',
    priority: 'high',
    messages: [
      {
        id: 1,
        content: 'Je n\'arrive pas à accéder au quiz de pharmacocinétique. J\'ai essayé plusieurs fois mais je reçois une erreur.',
        sender: 'apprenant',
        timestamp: '2023-06-10T14:30:00',
      },
      {
        id: 2,
        content: 'Nous avons bien reçu votre réclamation. Pouvez-vous nous préciser quel message d\'erreur vous recevez ?',
        sender: 'admin',
        timestamp: '2023-06-10T15:45:00',
      },
      {
        id: 3,
        content: 'J\'ai l\'erreur "Le quiz n\'est pas disponible pour le moment".',
        sender: 'apprenant',
        timestamp: '2023-06-10T16:20:00',
      },
    ],
  },
  {
    id: 2,
    title: 'Demande de prolongation',
    description: 'Je souhaite demander une prolongation pour le cours de pharmacie galénique.',
    status: 'resolved',
    createdAt: '2023-05-25T09:15:00',
    category: 'administratif',
    priority: 'medium',
    messages: [
      {
        id: 1,
        content: 'Je souhaite demander une prolongation pour le cours de pharmacie galénique car j\'ai eu des problèmes de santé.',
        sender: 'apprenant',
        timestamp: '2023-05-25T09:15:00',
      },
      {
        id: 2,
        content: 'Votre demande a été prise en compte. Pourriez-vous nous fournir un justificatif médical ?',
        sender: 'admin',
        timestamp: '2023-05-25T11:30:00',
      },
      {
        id: 3,
        content: 'Je vous ai envoyé le certificat médical par email.',
        sender: 'apprenant',
        timestamp: '2023-05-26T10:45:00',
      },
      {
        id: 4,
        content: 'Nous avons bien reçu votre justificatif. Votre prolongation est accordée jusqu\'au 15 juillet.',
        sender: 'admin',
        timestamp: '2023-05-27T14:20:00',
      },
    ],
  },
];

// Mock messages data
export const mockConversations = [
  {
    id: 1,
    name: 'Dr. Sarah Martin',
    role: 'formateur',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    lastMessage: 'N\'hésitez pas à me contacter si vous avez des questions sur le cours.',
    lastMessageTime: '2023-06-15T10:30:00',
    unread: 0,
    status: 'online',
  },
  {
    id: 2,
    name: 'Dr. Thomas Dupont',
    role: 'formateur',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    lastMessage: 'Le prochain quiz sera disponible demain.',
    lastMessageTime: '2023-06-14T16:45:00',
    unread: 2,
    status: 'offline',
  },
  {
    id: 3,
    name: 'Dr. Marie Laurent',
    role: 'formateur',
    avatar: 'https://randomuser.me/api/portraits/women/33.jpg',
    lastMessage: 'Bonjour, avez-vous terminé le module sur les interactions médicamenteuses ?',
    lastMessageTime: '2023-06-13T11:20:00',
    unread: 1,
    status: 'online',
  },
  {
    id: 4,
    name: 'Dr. Jean Moreau',
    role: 'formateur',
    avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
    lastMessage: 'Les documents supplémentaires sont disponibles dans la section ressources.',
    lastMessageTime: '2023-06-10T09:15:00',
    unread: 0,
    status: 'offline',
  },
  {
    id: 5,
    name: 'Support Technique',
    role: 'support',
    avatar: 'https://randomuser.me/api/portraits/lego/1.jpg',
    lastMessage: 'Votre problème a été résolu. N\'hésitez pas à nous contacter si vous avez d\'autres questions.',
    lastMessageTime: '2023-06-08T14:30:00',
    unread: 0,
    status: 'online',
  }
];

export const mockMessages = {
  1: [
    {
      id: 1,
      content: 'Bonjour, comment puis-je vous aider avec le cours de pharmacologie ?',
      sender: 'formateur',
      timestamp: '2023-06-10T09:30:00',
      isRead: true,
    },
    {
      id: 2,
      content: 'Bonjour Dr. Martin, j\'ai une question sur la pharmacocinétique.',
      sender: 'apprenant',
      timestamp: '2023-06-10T09:35:00',
      isRead: true,
    },
    {
      id: 3,
      content: 'Je vous écoute, quelle est votre question ?',
      sender: 'formateur',
      timestamp: '2023-06-10T09:37:00',
      isRead: true,
    },
    {
      id: 4,
      content: 'Je ne comprends pas bien la différence entre la biodisponibilité et la bioéquivalence.',
      sender: 'apprenant',
      timestamp: '2023-06-10T09:40:00',
      isRead: true,
    },
    {
      id: 5,
      content: 'La biodisponibilité est la fraction de la dose administrée qui atteint la circulation systémique, tandis que la bioéquivalence compare la biodisponibilité de deux formulations différentes du même médicament.',
      sender: 'formateur',
      timestamp: '2023-06-10T09:45:00',
      isRead: true,
    },
    {
      id: 6,
      content: 'Merci pour cette explication claire !',
      sender: 'apprenant',
      timestamp: '2023-06-10T09:50:00',
      isRead: true,
    },
    {
      id: 7,
      content: 'N\'hésitez pas à me contacter si vous avez des questions sur le cours.',
      sender: 'formateur',
      timestamp: '2023-06-15T10:30:00',
      isRead: true,
    },
  ],
  2: [
    {
      id: 1,
      content: 'Bonjour à tous, je voulais vous informer que les documents pour le cours de galénique sont maintenant disponibles.',
      sender: 'formateur',
      timestamp: '2023-06-12T14:20:00',
      isRead: true,
    },
    {
      id: 2,
      content: 'Merci Dr. Dupont. Est-ce que le quiz sera bientôt disponible ?',
      sender: 'apprenant',
      timestamp: '2023-06-12T14:30:00',
      isRead: true,
    },
    {
      id: 3,
      content: 'Oui, le quiz sera disponible demain après-midi.',
      sender: 'formateur',
      timestamp: '2023-06-12T14:35:00',
      isRead: true,
    },
    {
      id: 4,
      content: 'Parfait, merci !',
      sender: 'apprenant',
      timestamp: '2023-06-12T14:40:00',
      isRead: true,
    },
    {
      id: 5,
      content: 'Le prochain quiz sera disponible demain.',
      sender: 'formateur',
      timestamp: '2023-06-14T16:45:00',
      isRead: false,
    },
    {
      id: 6,
      content: 'N\'oubliez pas de réviser le chapitre sur les formes pharmaceutiques liquides.',
      sender: 'formateur',
      timestamp: '2023-06-14T16:47:00',
      isRead: false,
    },
  ],
  3: [
    {
      id: 1,
      content: 'Bonjour, j\'espère que vous allez bien.',
      sender: 'formateur',
      timestamp: '2023-06-13T11:15:00',
      isRead: true,
    },
    {
      id: 2,
      content: 'Bonjour Dr. Laurent, je vais bien merci.',
      sender: 'apprenant',
      timestamp: '2023-06-13T11:17:00',
      isRead: true,
    },
    {
      id: 3,
      content: 'Bonjour, avez-vous terminé le module sur les interactions médicamenteuses ?',
      sender: 'formateur',
      timestamp: '2023-06-13T11:20:00',
      isRead: false,
    },
  ],
  4: [
    {
      id: 1,
      content: 'Bonjour, je voulais vous informer que j\'ai ajouté des ressources supplémentaires pour le cours.',
      sender: 'formateur',
      timestamp: '2023-06-10T09:10:00',
      isRead: true,
    },
    {
      id: 2,
      content: 'Merci Dr. Moreau, où puis-je les trouver ?',
      sender: 'apprenant',
      timestamp: '2023-06-10T09:12:00',
      isRead: true,
    },
    {
      id: 3,
      content: 'Les documents supplémentaires sont disponibles dans la section ressources.',
      sender: 'formateur',
      timestamp: '2023-06-10T09:15:00',
      isRead: true,
    },
  ],
  5: [
    {
      id: 1,
      content: 'Bonjour, j\'ai un problème pour accéder au quiz de pharmacocinétique.',
      sender: 'apprenant',
      timestamp: '2023-06-08T14:15:00',
      isRead: true,
    },
    {
      id: 2,
      content: 'Bonjour, pouvez-vous me donner plus de détails sur l\'erreur que vous rencontrez ?',
      sender: 'formateur',
      timestamp: '2023-06-08T14:20:00',
      isRead: true,
    },
    {
      id: 3,
      content: 'J\'ai un message d\'erreur qui dit "Le quiz n\'est pas disponible pour le moment".',
      sender: 'apprenant',
      timestamp: '2023-06-08T14:25:00',
      isRead: true,
    },
    {
      id: 4,
      content: 'Votre problème a été résolu. N\'hésitez pas à nous contacter si vous avez d\'autres questions.',
      sender: 'formateur',
      timestamp: '2023-06-08T14:30:00',
      isRead: true,
    },
  ],
};

// Mock certificates data
export const mockCertificates = [
  {
    id: 1,
    coursId: 1,
    coursTitle: 'Introduction à la pharmacologie',
    dateObtention: '2023-06-20',
    numero: 'CERT-000123',
    apprenant: {
      id: 1,
      name: 'John Doe'
    }
  },
  {
    id: 2,
    coursId: 4,
    coursTitle: 'Biochimie médicale',
    dateObtention: '2023-05-15',
    numero: 'CERT-000118',
    apprenant: {
      id: 1,
      name: 'John Doe'
    }
  }
];

// Mock dashboard stats
export const mockDashboardStats = {
  coursesInProgress: 2,
  completedQuizzes: '3/6',
  certifications: 2,
  overallProgress: 35,
  courseProgress: [
    {
      id: 1,
      title: 'Introduction à la pharmacologie',
      progress: 75,
      color: '#4F46E5',
    },
    {
      id: 2,
      title: 'Pharmacie galénique',
      progress: 30,
      color: '#10B981',
    },
    {
      id: 3,
      title: 'Pharmacie clinique',
      progress: 0,
      color: '#F59E0B',
    },
  ],
  recentActivities: [
    {
      id: 1,
      title: 'Quiz complété: Pharmacocinétique',
      time: 'Il y a 2 jours',
      icon: 'check-circle',
      status: 'completed',
    },
    {
      id: 2,
      title: 'Nouveau cours disponible: Pharmacie clinique',
      time: 'Il y a 5 jours',
      icon: 'book-open-variant',
      status: 'new',
    },
    {
      id: 3,
      title: 'Module terminé: Pharmacocinétique',
      time: 'Il y a 1 semaine',
      icon: 'file-document-outline',
      status: 'completed',
    },
  ],
};
