import api from "./api";
import { QuizService } from "./QuizService";

const coursService = {
  // Get all courses for an apprenant - real API implementation
  getApprenantCours: async () => {
    try {
      console.log("Getting apprenant courses from real API");

      // Use the same endpoint as frontend_pfe
      const response = await api.get("/apprenant/cours");

      console.log("Courses API response:", response.data);

      // Handle the correct response format from backend
      // Backend returns: { apprenant: {...}, cours: [...] }
      const courses =
        response.data?.cours ||
        response.data?.["hydra:member"] ||
        response.data ||
        [];

      console.log("Extracted courses:", courses);

      return Array.isArray(courses) ? courses : [];
    } catch (error) {
      console.error("Error fetching apprenant courses:", error);

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      throw new Error("Impossible de récupérer vos cours");
    }
  },

  // Get course details - FIXED: Backend doesn't have /cours/{id} endpoint
  // We need to get course details from the apprenant courses list
  getCourseDetails: async (courseId) => {
    try {
      console.log(`Getting course details for ID: ${courseId}`);

      // FIXED: Backend doesn't have individual course details endpoint
      // Get all apprenant courses and find the specific one
      const response = await api.get("/apprenant/cours");
      const coursData = response.data?.cours || response.data || [];

      console.log("All courses API response:", coursData);

      // Find the specific course by ID
      const courseDetails = Array.isArray(coursData)
        ? coursData.find((course) => course.id == courseId)
        : null;

      if (!courseDetails) {
        throw new Error("Cours non trouvé");
      }

      // Ensure the course has all required fields for mobile display
      const processedCourse = {
        id: courseDetails.id,
        title: courseDetails.title || courseDetails.titre || courseDetails.nom,
        description: courseDetails.description,
        image: courseDetails.image,
        progress: courseDetails.progress || courseDetails.progression || 0,
        status: courseDetails.status || courseDetails.statut || "in-progress",
        instructor: courseDetails.instructor || courseDetails.formateur,
        duration: courseDetails.duration || courseDetails.duree,
        modules: courseDetails.modules || courseDetails.lecons || [],
        quizzes: courseDetails.quizzes || courseDetails.quiz || [],
        resources: courseDetails.resources || courseDetails.ressources || [],
        certificate: courseDetails.certificate || courseDetails.certificat,
        createdAt: courseDetails.createdAt || courseDetails.created_at,
        updatedAt: courseDetails.updatedAt || courseDetails.updated_at,
      };

      console.log("Processed course details:", processedCourse);

      return processedCourse;
    } catch (error) {
      console.error("Error fetching course details:", error);

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      if (error.response?.status === 404) {
        throw new Error("Cours non trouvé");
      }

      throw new Error("Impossible de récupérer les détails du cours");
    }
  },

  // Get course progress - real API implementation
  getCourseProgress: async (courseId) => {
    try {
      console.log(`Getting course progress for ID: ${courseId}`);

      // Use the same endpoint pattern as frontend_pfe
      const response = await api.get(`/cours/${courseId}/progress`);

      console.log("Course progress API response:", response.data);

      return response.data;
    } catch (error) {
      console.error("Error fetching course progress:", error);

      // Return default progress if API fails
      return {
        courseId: parseInt(courseId),
        progress: 0,
        completedModules: 0,
        totalModules: 0,
        lastAccessedModule: 0,
        timeSpent: 0,
      };
    }
  },

  // Mark course module as completed - real API implementation
  markModuleCompleted: async (courseId, moduleId) => {
    try {
      console.log(
        `Marking module completed - Course: ${courseId}, Module: ${moduleId}`
      );

      const response = await api.post(
        `/cours/${courseId}/modules/${moduleId}/complete`
      );

      console.log("Mark module completed API response:", response.data);

      return {
        success: true,
        message: "Module marqué comme terminé",
        ...response.data,
      };
    } catch (error) {
      console.error("Error marking module as completed:", error);

      throw new Error("Impossible de marquer le module comme terminé");
    }
  },

  // Get course quizzes - real API implementation
  getCourseQuizzes: async (courseId) => {
    try {
      console.log(`Getting course quizzes for ID: ${courseId}`);

      // Use the same endpoint as frontend_pfe
      const response = await api.get(`/quiz?cours=${courseId}`);

      console.log("Course quizzes API response:", response.data);

      // Handle different response formats (same as frontend)
      const quizzes = response.data?.["hydra:member"] || response.data || [];

      return {
        quizzes: Array.isArray(quizzes) ? quizzes : [],
        courseId: courseId,
      };
    } catch (error) {
      console.error("Error fetching course quizzes:", error);

      return {
        quizzes: [],
        courseId: courseId,
      };
    }
  },

  // Submit quiz answers - real API implementation
  submitQuizAnswers: async (quizId, answers) => {
    try {
      console.log(`Submitting quiz answers for quiz ID: ${quizId}`);
      console.log("Answers submitted:", answers);

      const response = await api.post(`/quiz/${quizId}/submit`, {
        answers: answers,
      });

      console.log("Submit quiz API response:", response.data);

      return {
        success: true,
        ...response.data,
      };
    } catch (error) {
      console.error("Error submitting quiz answers:", error);

      throw new Error("Impossible de soumettre les réponses du quiz");
    }
  },

  // Get quiz details by IDModule - real API implementation
  getQuizByIdModule: async (idModule) => {
    try {
      console.log(`Getting quiz details for IDModule: ${idModule}`);

      const response = await api.get(`/quiz/${encodeURIComponent(idModule)}`);

      console.log("Quiz details API response:", response.data);

      return response.data;
    } catch (error) {
      console.error("Error fetching quiz details:", error);

      // Return mock data for demo purposes
      return {
        id: Math.floor(Math.random() * 1000),
        IDModule: idModule,
        Nom_FR: "Quiz de démonstration",
        Nom_EN: "Demo Quiz",
        Category: "Demo",
        Type: "Evaluation",
        MainSurface: false,
        Main: 0,
        Surface: 0,
        questions: [
          {
            id: 1,
            question: "Question de démonstration 1",
            answers: ["Réponse A", "Réponse B", "Réponse C", "Réponse D"],
            correctAnswer: 0,
          },
          {
            id: 2,
            question: "Question de démonstration 2",
            answers: ["Option 1", "Option 2", "Option 3", "Option 4"],
            correctAnswer: 1,
          },
        ],
      };
    }
  },

  // Get progression for apprenant and course - real API implementation
  getProgressionByApprenantAndCours: async (apprenantId, coursId) => {
    try {
      console.log(
        `Getting progression for apprenant ${apprenantId} and course ${coursId}`
      );

      const response = await api.get(
        `/progression/apprenant/${apprenantId}/cours/${coursId}`
      );

      console.log("Progression API response:", response.data);

      return response.data;
    } catch (error) {
      console.error("Error fetching progression:", error);

      // Return default progression data
      return {
        progress_percentage: 0,
        quizzes_total: 0,
        quizzes_passed: 0,
        quiz_evaluations: [],
        is_completed: false,
        certificat: null,
      };
    }
  },

  // Create or update evaluation - real API implementation
  createEvaluation: async (evaluationData) => {
    try {
      console.log("Creating evaluation:", evaluationData);

      const response = await api.post("/evaluation", evaluationData);

      console.log("Create evaluation API response:", response.data);

      return response.data;
    } catch (error) {
      console.error("Error creating evaluation:", error);
      throw new Error("Impossible de créer l'évaluation");
    }
  },

  // Get evaluation by quiz and apprenant - real API implementation
  getEvaluationByQuizAndApprenant: async (quizId, apprenantId) => {
    try {
      console.log(
        `Getting evaluation for quiz ${quizId} and apprenant ${apprenantId}`
      );

      const response = await api.get(
        `/evaluation/quiz/${quizId}/apprenant/${apprenantId}`
      );

      console.log("Evaluation API response:", response.data);

      return response.data;
    } catch (error) {
      console.error("Error fetching evaluation:", error);
      return null;
    }
  },

  // Generate certificate - real API implementation
  generateCertificat: async (apprenantId, coursId) => {
    try {
      console.log(
        `Generating certificate for apprenant ${apprenantId} and course ${coursId}`
      );

      const response = await api.post("/certificat/generate-direct", {
        apprenantId,
        coursId,
      });

      console.log("Generate certificate API response:", response.data);

      return response.data;
    } catch (error) {
      console.error("Error generating certificate:", error);
      throw new Error("Impossible de générer le certificat");
    }
  },

  // Get certificates by apprenant - real API implementation
  getCertificatsByApprenant: async (apprenantId) => {
    try {
      console.log(`Getting certificates for apprenant ${apprenantId}`);

      const response = await api.get(`/certificat/apprenant/${apprenantId}`);

      console.log("Certificates API response:", response.data);

      return response.data.certificats || [];
    } catch (error) {
      console.error("Error fetching certificates:", error);
      return [];
    }
  },

  // Check and get certificate for course - real API implementation
  checkCertificate: async (apprenantId, coursId) => {
    try {
      console.log(
        `Checking certificate for apprenant ${apprenantId} and course ${coursId}`
      );

      const response = await api.get(
        `/certificat/check-and-generate/${apprenantId}/${coursId}`
      );

      console.log("Check certificate API response:", response.data);

      return response.data;
    } catch (error) {
      console.error("Error checking certificate:", error);
      return null;
    }
  },

  // Get all courses (for admin/formateur) - real API implementation
  getAllCours: async () => {
    try {
      console.log("Getting all courses from real API");

      const response = await api.get("/cours");

      console.log("All courses API response:", response.data);

      // Handle different response formats (same as frontend)
      const courses = response.data?.["hydra:member"] || response.data || [];

      return Array.isArray(courses) ? courses : [];
    } catch (error) {
      console.error("Error fetching all courses:", error);

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      throw new Error("Impossible de récupérer les cours");
    }
  },
};

export default coursService;
