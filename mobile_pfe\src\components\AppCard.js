import React, { useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { ThemeContext } from '../contexts/ThemeContext';

const AppCard = ({
  children,
  title,
  subtitle,
  onPress,
  style,
  contentStyle,
  headerStyle,
  titleStyle,
  subtitleStyle,
  showBorder = true,
  showShadow = true,
  disabled = false,
  renderHeader,
  renderFooter,
  icon,
  iconColor
}) => {
  const { theme, isDarkMode } = useContext(ThemeContext);

  const cardStyles = [
    styles.card,
    {
      backgroundColor: theme.card,
      borderColor: showBorder ? theme.border : 'transparent',
    },
    showShadow && {
      ...Platform.select({
        ios: {
          shadowColor: theme.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: isDarkMode ? 0.3 : 0.1,
          shadowRadius: 4,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    style
  ];

  const renderCardContent = () => (
    <>
      {(title || subtitle || renderHeader) && (
        <View style={[styles.cardHeader, headerStyle]}>
          {renderHeader ? (
            renderHeader()
          ) : (
            <View style={styles.headerContent}>
              {icon && (
                <View style={[styles.iconContainer, { backgroundColor: iconColor ? `${iconColor}20` : `${theme.primary}20` }]}>
                  <MaterialCommunityIcons
                    name={icon}
                    size={18}
                    color={iconColor || theme.primary}
                  />
                </View>
              )}
              <View style={styles.titleContainer}>
                {title && (
                  <Text style={[
                    styles.cardTitle,
                    { color: theme.text.primary },
                    titleStyle
                  ]}>
                    {title}
                  </Text>
                )}
                {subtitle && (
                  <Text style={[
                    styles.cardSubtitle,
                    { color: theme.text.secondary },
                    subtitleStyle
                  ]}>
                    {subtitle}
                  </Text>
                )}
              </View>
            </View>
          )}
        </View>
      )}

      <View style={[styles.cardContent, contentStyle]}>
        {children}
      </View>

      {renderFooter && (
        <View style={styles.cardFooter}>
          {renderFooter()}
        </View>
      )}
    </>
  );

  if (onPress) {
    return (
      <TouchableOpacity
        style={cardStyles}
        onPress={onPress}
        activeOpacity={0.7}
        disabled={disabled}
      >
        {renderCardContent()}
      </TouchableOpacity>
    );
  }

  return (
    <View style={cardStyles}>
      {renderCardContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    overflow: 'hidden',
  },
  cardHeader: {
    padding: 16,
    paddingBottom: 12,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  titleContainer: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
  },
  cardContent: {
    padding: 16,
    paddingTop: 0,
  },
  cardFooter: {
    padding: 16,
    paddingTop: 0,
  },
});

export default AppCard;
