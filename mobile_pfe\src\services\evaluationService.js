import api from './api';

/**
 * Service pour gérer les évaluations (identique au frontend web)
 */
const evaluationService = {
  /**
   * Crée une évaluation pour un quiz
   * @param {Object} evaluationData - Données de l'évaluation
   * @returns {Promise<Object>} - Réponse de l'API
   */
  createEvaluation: async (evaluationData) => {
    try {
      console.log('Mobile: Creating evaluation:', evaluationData);

      const response = await api.post('/evaluation', evaluationData);

      console.log('Mobile: Evaluation creation response:', response.data);

      return response.data;
    } catch (error) {
      console.error('Mobile: Error creating evaluation:', error);

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      if (error.response?.status === 400) {
        throw new Error("Données d'évaluation invalides");
      }

      throw new Error("Impossible de créer l'évaluation");
    }
  },

  /**
   * Récupère une évaluation par quiz et apprenant
   * @param {number} quizId - ID du quiz
   * @param {number} apprenantId - ID de l'apprenant
   * @returns {Promise<Object>} - Données de l'évaluation
   */
  getEvaluationByQuizAndApprenant: async (quizId, apprenantId) => {
    try {
      console.log(`Mobile: Getting evaluation for quiz ${quizId} and apprenant ${apprenantId}`);

      const response = await api.get(`/evaluation/quiz/${quizId}/apprenant/${apprenantId}`);

      console.log('Mobile: Evaluation data received:', response.data);

      return response.data;
    } catch (error) {
      console.error('Mobile: Error fetching evaluation:', error);

      if (error.response?.status === 404) {
        console.warn(`Mobile: No evaluation found for quiz ${quizId} and apprenant ${apprenantId}`);
        return null;
      }

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      return null;
    }
  },

  /**
   * Met à jour une évaluation
   * @param {number} evaluationId - ID de l'évaluation
   * @param {Object} updateData - Données à mettre à jour
   * @returns {Promise<Object>} - Réponse de l'API
   */
  updateEvaluation: async (evaluationId, updateData) => {
    try {
      console.log(`Mobile: Updating evaluation ${evaluationId}:`, updateData);

      const response = await api.put(`/evaluation/${evaluationId}`, updateData);

      console.log('Mobile: Evaluation update response:', response.data);

      return response.data;
    } catch (error) {
      console.error('Mobile: Error updating evaluation:', error);

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      if (error.response?.status === 404) {
        throw new Error("Évaluation non trouvée");
      }

      throw new Error("Impossible de mettre à jour l'évaluation");
    }
  },

  /**
   * Récupère toutes les évaluations d'un apprenant
   * @param {number} apprenantId - ID de l'apprenant
   * @returns {Promise<Array>} - Liste des évaluations
   */
  getEvaluationsByApprenant: async (apprenantId) => {
    try {
      console.log(`Mobile: Getting evaluations for apprenant ${apprenantId}`);

      const response = await api.get(`/evaluation/apprenant/${apprenantId}`);

      if (!response.data) {
        return [];
      }

      const evaluations = response.data.evaluations || response.data || [];
      console.log(`Mobile: Found ${evaluations.length} evaluations for apprenant ${apprenantId}`);

      return evaluations;
    } catch (error) {
      console.error('Mobile: Error fetching evaluations:', error);

      if (error.response?.status === 404) {
        console.warn(`Mobile: No evaluations found for apprenant ${apprenantId}`);
        return [];
      }

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      return [];
    }
  },

  /**
   * Soumet les réponses d'un quiz et crée une évaluation
   * @param {number} quizId - ID du quiz
   * @param {number} apprenantId - ID de l'apprenant
   * @param {Array} answers - Réponses du quiz
   * @param {Object} checkedActions - Actions cochées
   * @returns {Promise<Object>} - Résultat de l'évaluation
   */
  submitQuizAndCreateEvaluation: async (quizId, apprenantId, answers, checkedActions = {}) => {
    try {
      console.log('Mobile: Submitting quiz and creating evaluation:', {
        quizId,
        apprenantId,
        answers,
        checkedActions
      });

      // Calculer le score basé sur les réponses (logique simplifiée)
      const totalQuestions = answers.length;
      const correctAnswers = Math.floor(Math.random() * totalQuestions); // Simulation
      const score = Math.round((correctAnswers / totalQuestions) * 100);
      
      // Déterminer le statut basé sur le score et les actions
      const allActionsChecked = Object.values(checkedActions).every(checked => checked);
      const statut = (score >= 70 && allActionsChecked) ? 'Satisfaisant' : 'Non Satisfaisant';

      // Créer l'évaluation
      const evaluationData = {
        quizId: parseInt(quizId),
        apprenantId: parseInt(apprenantId),
        statut: statut,
        score: score,
        answers: answers,
        checkedActions: checkedActions
      };

      const response = await evaluationService.createEvaluation(evaluationData);

      return {
        success: true,
        score: score,
        statut: statut,
        evaluation: response.evaluation,
        certificate: response.certificate
      };
    } catch (error) {
      console.error('Mobile: Error submitting quiz and creating evaluation:', error);
      throw error;
    }
  }
};

export default evaluationService;
