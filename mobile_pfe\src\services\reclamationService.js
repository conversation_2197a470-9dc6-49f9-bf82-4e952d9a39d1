import api from './api';

const reclamationService = {
  // Get all reclamations for the current user - real API implementation
  getUserReclamations: async () => {
    try {
      console.log('Getting user reclamations from real API');

      // Use the same endpoint as frontend_pfe
      const response = await api.get('/reclamation/user');

      console.log('User reclamations API response:', response.data);

      // Handle the correct response format from backend
      // Backend may return: { reclamations: [...] } or direct array
      const reclamations = response.data?.reclamations || response.data?.['hydra:member'] || response.data || [];

      console.log('Extracted reclamations:', reclamations);

      return Array.isArray(reclamations) ? reclamations : [];
    } catch (error) {
      console.error('Error fetching user reclamations:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      throw new Error('Impossible de récupérer vos réclamations');
    }
  },

  // Get details of a specific reclamation - real API implementation
  getReclamationDetails: async (reclamationId) => {
    try {
      console.log(`Getting reclamation details for ID: ${reclamationId}`);

      // Use the same endpoint as frontend_pfe
      const response = await api.get(`/reclamation/${reclamationId}`);

      console.log('Reclamation details API response:', response.data);

      return response.data;
    } catch (error) {
      console.error('Error fetching reclamation details:', error);

      if (error.response?.status === 404) {
        throw new Error('Réclamation non trouvée');
      }

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      throw new Error('Impossible de récupérer les détails de la réclamation');
    }
  },

  // Create a new reclamation - FIXED: Use correct field names that backend expects
  createReclamation: async (reclamationData) => {
    try {
      console.log('Creating reclamation with real API', reclamationData);

      // FIXED: Backend expects 'subject' and 'message', not 'title' and 'description'
      const response = await api.post('/reclamation', {
        subject: reclamationData.title || reclamationData.subject,
        message: reclamationData.description || reclamationData.message
      });

      console.log('Create reclamation API response:', response.data);

      return {
        success: true,
        message: 'Réclamation créée avec succès',
        reclamation: response.data
      };
    } catch (error) {
      console.error('Error creating reclamation:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      if (error.response?.status === 400) {
        throw new Error('Veuillez remplir tous les champs requis');
      }

      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }

      throw new Error('Impossible de créer la réclamation');
    }
  },

  // Reply to a reclamation - FIXED: Use correct field name that backend expects
  replyToReclamation: async (reclamationId, content) => {
    try {
      console.log(`Replying to reclamation ${reclamationId} with real API`);

      // FIXED: Backend expects 'response', not 'content'
      const response = await api.post(`/reclamation/${reclamationId}/reply`, {
        response: content
      });

      console.log('Reply to reclamation API response:', response.data);

      return {
        success: true,
        message: 'Réponse envoyée avec succès',
        data: response.data
      };
    } catch (error) {
      console.error('Error replying to reclamation:', error);

      if (error.response?.status === 404) {
        throw new Error('Réclamation non trouvée');
      }

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      throw new Error('Impossible d\'envoyer la réponse');
    }
  },

  // Cancel/Close a reclamation - real API implementation
  cancelReclamation: async (reclamationId) => {
    try {
      console.log(`Canceling reclamation ${reclamationId} with real API`);

      // Use the same endpoint pattern as frontend_pfe
      const response = await api.put(`/reclamation/${reclamationId}/status`, {
        status: 'closed'
      });

      console.log('Cancel reclamation API response:', response.data);

      return {
        success: true,
        message: 'Réclamation fermée avec succès'
      };
    } catch (error) {
      console.error('Error canceling reclamation:', error);

      if (error.response?.status === 404) {
        throw new Error('Réclamation non trouvée');
      }

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      throw new Error('Impossible de fermer la réclamation');
    }
  },
};

export default reclamationService;
