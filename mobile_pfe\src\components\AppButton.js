import React, { useContext } from 'react';
import { 
  TouchableOpacity, 
  Text, 
  StyleSheet, 
  ActivityIndicator,
  View
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { ThemeContext } from '../contexts/ThemeContext';

const AppButton = ({
  title,
  onPress,
  variant = 'primary', // primary, secondary, outline, ghost, danger
  size = 'medium', // small, medium, large
  fullWidth = false,
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  style,
  textStyle,
  ...props
}) => {
  const { theme, isDarkMode } = useContext(ThemeContext);
  
  // Determine button background color based on variant
  const getBackgroundColor = () => {
    if (disabled) {
      return isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)';
    }
    
    switch (variant) {
      case 'primary':
        return theme.primary;
      case 'secondary':
        return theme.secondary;
      case 'danger':
        return theme.danger;
      case 'outline':
      case 'ghost':
        return 'transparent';
      default:
        return theme.primary;
    }
  };
  
  // Determine button text color based on variant
  const getTextColor = () => {
    if (disabled) {
      return isDarkMode ? 'rgba(255,255,255,0.4)' : 'rgba(0,0,0,0.4)';
    }
    
    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'danger':
        return theme.text.inverse;
      case 'outline':
        if (variant === 'primary') return theme.primary;
        if (variant === 'secondary') return theme.secondary;
        if (variant === 'danger') return theme.danger;
        return theme.text.primary;
      case 'ghost':
        if (variant === 'primary') return theme.primary;
        if (variant === 'secondary') return theme.secondary;
        if (variant === 'danger') return theme.danger;
        return theme.text.primary;
      default:
        return theme.text.inverse;
    }
  };
  
  // Determine button border color based on variant
  const getBorderColor = () => {
    if (disabled) {
      return isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)';
    }
    
    if (variant === 'outline') {
      if (variant === 'primary') return theme.primary;
      if (variant === 'secondary') return theme.secondary;
      if (variant === 'danger') return theme.danger;
      return theme.border;
    }
    
    return 'transparent';
  };
  
  // Determine button size
  const getButtonSize = () => {
    switch (size) {
      case 'small':
        return styles.buttonSmall;
      case 'large':
        return styles.buttonLarge;
      default:
        return styles.buttonMedium;
    }
  };
  
  // Determine text size
  const getTextSize = () => {
    switch (size) {
      case 'small':
        return styles.textSmall;
      case 'large':
        return styles.textLarge;
      default:
        return styles.textMedium;
    }
  };
  
  // Determine icon size
  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 16;
      case 'large':
        return 24;
      default:
        return 20;
    }
  };
  
  return (
    <TouchableOpacity
      style={[
        styles.button,
        getButtonSize(),
        {
          backgroundColor: getBackgroundColor(),
          borderColor: getBorderColor(),
          borderWidth: variant === 'outline' ? 1 : 0,
          width: fullWidth ? '100%' : 'auto',
        },
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      {...props}
    >
      {loading ? (
        <ActivityIndicator 
          size="small" 
          color={getTextColor()} 
        />
      ) : (
        <View style={styles.contentContainer}>
          {icon && iconPosition === 'left' && (
            <MaterialCommunityIcons
              name={icon}
              size={getIconSize()}
              color={getTextColor()}
              style={styles.iconLeft}
            />
          )}
          
          <Text style={[
            styles.text,
            getTextSize(),
            { color: getTextColor() },
            textStyle,
          ]}>
            {title}
          </Text>
          
          {icon && iconPosition === 'right' && (
            <MaterialCommunityIcons
              name={icon}
              size={getIconSize()}
              color={getTextColor()}
              style={styles.iconRight}
            />
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonSmall: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    minHeight: 32,
  },
  buttonMedium: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    minHeight: 40,
  },
  buttonLarge: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    minHeight: 48,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  textSmall: {
    fontSize: 12,
  },
  textMedium: {
    fontSize: 14,
  },
  textLarge: {
    fontSize: 16,
  },
  iconLeft: {
    marginRight: 8,
  },
  iconRight: {
    marginLeft: 8,
  },
});

export default AppButton;
