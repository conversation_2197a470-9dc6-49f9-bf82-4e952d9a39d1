import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  ActivityIndicator,
  Alert
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AuthContext } from '../../contexts/AuthContext';
import { ThemeContext } from '../../contexts/ThemeContext';
import messagerieService from '../../services/messagerieService';
import LoadingScreen from '../../components/LoadingScreen';

const ConversationItem = ({ conversation, onPress }) => {
  const { theme, isDarkMode } = useContext(ThemeContext);

  // Format the time to display
  const formatTime = (timestamp) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      // Today: show time
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      // Yesterday
      return 'Hier';
    } else if (diffDays < 7) {
      // This week: show day name
      return date.toLocaleDateString([], { weekday: 'long' });
    } else {
      // Older: show date
      return date.toLocaleDateString([], { day: 'numeric', month: 'short' });
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.conversationItem,
        { borderBottomColor: theme.border }
      ]}
      onPress={onPress}
    >
      <View style={styles.avatarContainer}>
        <Image
          source={
            conversation.avatar
              ? { uri: conversation.avatar }
              : require('../../../assets/default-avatar.png')
          }
          style={styles.conversationAvatar}
        />
        {conversation.status === 'online' && (
          <View style={[styles.onlineIndicator, { backgroundColor: '#4CAF50' }]} />
        )}
      </View>

      <View style={styles.conversationContent}>
        <View style={styles.conversationHeader}>
          <Text style={[styles.conversationName, { color: theme.text.primary }]}>
            {conversation.name || conversation.formateurName || conversation.formateur?.name || conversation.formateur?.nom || 'Formateur'}
          </Text>
          <Text style={[styles.conversationTime, { color: theme.text.tertiary }]}>
            {formatTime(conversation.lastMessageTime)}
          </Text>
        </View>

        <View style={styles.conversationFooter}>
          <Text
            style={[
              styles.conversationLastMessage,
              {
                color: conversation.unread > 0 ? theme.text.primary : theme.text.secondary,
                fontWeight: conversation.unread > 0 ? 'bold' : 'normal'
              }
            ]}
            numberOfLines={1}
          >
            {conversation.lastMessage || 'Aucun message'}
          </Text>

          {conversation.unread > 0 && (
            <View style={[styles.unreadBadge, { backgroundColor: theme.primary }]}>
              <Text style={styles.unreadBadgeText}>{conversation.unread}</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const ConversationListScreen = () => {
  const navigation = useNavigation();
  const { user } = useContext(AuthContext);
  const { theme, isDarkMode } = useContext(ThemeContext);
  const [loading, setLoading] = useState(true);
  const [conversations, setConversations] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchConversations();
  }, []);

  const fetchConversations = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await messagerieService.getConversations();
      setConversations(data);
    } catch (err) {
      console.error('Error fetching conversations:', err);
      setError('Impossible de charger les conversations');
    } finally {
      setLoading(false);
    }
  };

  const handleConversationPress = (conversation) => {
    navigation.navigate('ConversationDetail', {
      conversationId: conversation.id,
      name: conversation.name || conversation.formateurName || conversation.formateur?.name || conversation.formateur?.nom || 'Formateur',
      avatar: conversation.avatar
    });
  };

  const filteredConversations = conversations.filter(conversation => {
    // Handle different possible name fields and ensure they exist
    const name = conversation.name || conversation.formateurName || conversation.formateur?.name || conversation.formateur?.nom || '';
    return name.toLowerCase().includes(searchQuery.toLowerCase());
  });

  if (loading) {
    return <LoadingScreen message="Chargement des conversations..." />;
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: isDarkMode ? theme.card : '#F3F4F6' }]}>
        <MaterialCommunityIcons name="magnify" size={20} color={theme.text.secondary} />
        <TextInput
          style={[styles.searchInput, { color: theme.text.primary }]}
          placeholder="Rechercher..."
          placeholderTextColor={theme.text.tertiary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <MaterialCommunityIcons name="close-circle" size={20} color={theme.text.secondary} />
          </TouchableOpacity>
        )}
      </View>

      {error ? (
        <View style={styles.errorContainer}>
          <MaterialCommunityIcons name="alert-circle" size={48} color={theme.danger} />
          <Text style={[styles.errorText, { color: theme.text.primary }]}>{error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.primary }]}
            onPress={fetchConversations}
          >
            <Text style={[styles.retryButtonText, { color: theme.text.inverse }]}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={filteredConversations}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <ConversationItem
              conversation={item}
              onPress={() => handleConversationPress(item)}
            />
          )}
          contentContainerStyle={styles.conversationsList}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <MaterialCommunityIcons name="message-text-outline" size={48} color={theme.text.tertiary} />
              <Text style={[styles.emptyText, { color: theme.text.primary }]}>
                {searchQuery.length > 0
                  ? 'Aucune conversation trouvée'
                  : 'Aucune conversation'}
              </Text>
              <Text style={[styles.emptySubtext, { color: theme.text.secondary }]}>
                {searchQuery.length > 0
                  ? 'Essayez avec un autre terme de recherche'
                  : 'Vous n\'avez pas encore de conversations'}
              </Text>
            </View>
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 16,
    marginVertical: 12,
    borderRadius: 20,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    paddingVertical: 8,
  },
  conversationsList: {
    flexGrow: 1,
  },
  conversationItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
  },
  avatarContainer: {
    position: 'relative',
  },
  conversationAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 14,
    height: 14,
    borderRadius: 7,
    borderWidth: 2,
    borderColor: 'white',
  },
  conversationContent: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'center',
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  conversationName: {
    fontSize: 16,
    fontWeight: '600',
  },
  conversationTime: {
    fontSize: 12,
  },
  conversationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  conversationLastMessage: {
    fontSize: 14,
    flex: 1,
  },
  unreadBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
    marginLeft: 8,
  },
  unreadBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 16,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
});

export default ConversationListScreen;
