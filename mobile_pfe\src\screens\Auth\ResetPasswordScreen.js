import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { ThemeContext } from '../../contexts/ThemeContext';
import authService from '../../services/authService';

const ResetPasswordScreen = ({ route, navigation }) => {
  const { theme } = useContext(ThemeContext);
  const { token } = route.params || {};
  
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Validate password
  const validatePassword = () => {
    if (password.length < 8) {
      return 'Le mot de passe doit contenir au moins 8 caractères';
    }
    if (!/[A-Z]/.test(password)) {
      return 'Le mot de passe doit contenir au moins une lettre majuscule';
    }
    if (!/[a-z]/.test(password)) {
      return 'Le mot de passe doit contenir au moins une lettre minuscule';
    }
    if (!/[0-9]/.test(password)) {
      return 'Le mot de passe doit contenir au moins un chiffre';
    }
    return null;
  };
  
  // Handle reset password
  const handleResetPassword = async () => {
    try {
      setError(null);
      
      // Validate token
      if (!token) {
        setError('Token de réinitialisation invalide');
        return;
      }
      
      // Validate password
      const passwordError = validatePassword();
      if (passwordError) {
        setError(passwordError);
        return;
      }
      
      // Validate confirm password
      if (password !== confirmPassword) {
        setError('Les mots de passe ne correspondent pas');
        return;
      }
      
      setIsLoading(true);
      
      // Call API to reset password
      const response = await authService.resetPassword(token, password, confirmPassword);
      
      // Show success message
      Alert.alert(
        'Succès',
        'Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.',
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('Login'),
          },
        ]
      );
    } catch (error) {
      setError(error.message || 'Une erreur est survenue lors de la réinitialisation du mot de passe');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <ScrollView
      contentContainerStyle={[
        styles.container,
        { backgroundColor: theme.background }
      ]}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.header}>
        <MaterialCommunityIcons
          name="lock-reset"
          size={64}
          color={theme.primary}
        />
        <Text style={[styles.title, { color: theme.text.primary }]}>
          Réinitialiser votre mot de passe
        </Text>
        <Text style={[styles.subtitle, { color: theme.text.secondary }]}>
          Veuillez entrer votre nouveau mot de passe
        </Text>
      </View>
      
      {error && (
        <View style={[styles.errorContainer, { backgroundColor: theme.danger + '20' }]}>
          <MaterialCommunityIcons name="alert-circle" size={20} color={theme.danger} />
          <Text style={[styles.errorText, { color: theme.danger }]}>{error}</Text>
        </View>
      )}
      
      <View style={styles.form}>
        {/* Password Input */}
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: theme.text.primary }]}>
            Nouveau mot de passe
          </Text>
          <View style={[styles.inputContainer, { backgroundColor: theme.input.background, borderColor: theme.border }]}>
            <TextInput
              style={[styles.input, { color: theme.text.primary }]}
              placeholder="Entrez votre nouveau mot de passe"
              placeholderTextColor={theme.text.tertiary}
              secureTextEntry={!showPassword}
              value={password}
              onChangeText={setPassword}
            />
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => setShowPassword(!showPassword)}
            >
              <MaterialCommunityIcons
                name={showPassword ? 'eye-off' : 'eye'}
                size={24}
                color={theme.text.tertiary}
              />
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Confirm Password Input */}
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: theme.text.primary }]}>
            Confirmer le mot de passe
          </Text>
          <View style={[styles.inputContainer, { backgroundColor: theme.input.background, borderColor: theme.border }]}>
            <TextInput
              style={[styles.input, { color: theme.text.primary }]}
              placeholder="Confirmez votre nouveau mot de passe"
              placeholderTextColor={theme.text.tertiary}
              secureTextEntry={!showConfirmPassword}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
            />
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              <MaterialCommunityIcons
                name={showConfirmPassword ? 'eye-off' : 'eye'}
                size={24}
                color={theme.text.tertiary}
              />
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Password Requirements */}
        <View style={styles.requirementsContainer}>
          <Text style={[styles.requirementsTitle, { color: theme.text.secondary }]}>
            Le mot de passe doit contenir:
          </Text>
          <View style={styles.requirement}>
            <MaterialCommunityIcons
              name={password.length >= 8 ? 'check-circle' : 'circle-outline'}
              size={16}
              color={password.length >= 8 ? theme.success : theme.text.tertiary}
            />
            <Text style={[styles.requirementText, { color: theme.text.secondary }]}>
              Au moins 8 caractères
            </Text>
          </View>
          <View style={styles.requirement}>
            <MaterialCommunityIcons
              name={/[A-Z]/.test(password) ? 'check-circle' : 'circle-outline'}
              size={16}
              color={/[A-Z]/.test(password) ? theme.success : theme.text.tertiary}
            />
            <Text style={[styles.requirementText, { color: theme.text.secondary }]}>
              Au moins une lettre majuscule
            </Text>
          </View>
          <View style={styles.requirement}>
            <MaterialCommunityIcons
              name={/[a-z]/.test(password) ? 'check-circle' : 'circle-outline'}
              size={16}
              color={/[a-z]/.test(password) ? theme.success : theme.text.tertiary}
            />
            <Text style={[styles.requirementText, { color: theme.text.secondary }]}>
              Au moins une lettre minuscule
            </Text>
          </View>
          <View style={styles.requirement}>
            <MaterialCommunityIcons
              name={/[0-9]/.test(password) ? 'check-circle' : 'circle-outline'}
              size={16}
              color={/[0-9]/.test(password) ? theme.success : theme.text.tertiary}
            />
            <Text style={[styles.requirementText, { color: theme.text.secondary }]}>
              Au moins un chiffre
            </Text>
          </View>
        </View>
        
        {/* Submit Button */}
        <TouchableOpacity
          style={[
            styles.button,
            { backgroundColor: theme.primary },
            isLoading && styles.buttonDisabled
          ]}
          onPress={handleResetPassword}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFFFFF" size="small" />
          ) : (
            <Text style={styles.buttonText}>Réinitialiser le mot de passe</Text>
          )}
        </TouchableOpacity>
        
        {/* Back to Login */}
        <TouchableOpacity
          style={styles.linkButton}
          onPress={() => navigation.navigate('Login')}
        >
          <Text style={[styles.linkText, { color: theme.primary }]}>
            Retour à la connexion
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
  },
  form: {
    width: '100%',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  iconButton: {
    padding: 8,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    marginLeft: 8,
    flex: 1,
  },
  button: {
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  linkButton: {
    alignItems: 'center',
    marginTop: 16,
    padding: 8,
  },
  linkText: {
    fontSize: 16,
  },
  requirementsContainer: {
    marginTop: 8,
  },
  requirementsTitle: {
    fontSize: 14,
    marginBottom: 8,
  },
  requirement: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  requirementText: {
    fontSize: 14,
    marginLeft: 8,
  },
});

export default ResetPasswordScreen;
