import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_URL, API_TIMEOUT } from '../config/api';

console.log('API Service initialized with URL:', API_URL);

// Create an axios instance with default config matching frontend_pfe
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: API_TIMEOUT, // Same timeout as frontend_pfe
  // Disable SSL verification for development (React Native specific)
  validateStatus: function (status) {
    return status < 500; // Accept any status code less than 500
  },
});

// Add JWT token to requests
api.interceptors.request.use(
  async (config) => {
    try {
      // Get the token from storage
      const token = await AsyncStorage.getItem('userToken');

      // If token exists, add it to the headers
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      return config;
    } catch (error) {
      console.error('Error in request interceptor:', error);
      return config;
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle response errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    // Handle 401 Unauthorized errors (expired token)
    if (error.response && error.response.status === 401) {
      // Clear token from storage
      await AsyncStorage.removeItem('userToken');
      await AsyncStorage.removeItem('userData');

      // You could redirect to login screen here if needed
      // For now, we'll just log the error
      console.error('Authentication error: Token expired or invalid');
    }

    return Promise.reject(error);
  }
);

// Enable this for development to see API requests and responses
api.interceptors.request.use(request => {
  console.log('API Request:', request.method.toUpperCase(), request.url);
  if (request.data) {
    console.log('Request data:', request.data);
  }
  return request;
});

api.interceptors.response.use(
  response => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  error => {
    if (error.response) {
      console.error('API Error:', error.response.status, error.response.data);
    } else {
      console.error('API Error:', error.message);
    }
    return Promise.reject(error);
  }
);

export default api;
