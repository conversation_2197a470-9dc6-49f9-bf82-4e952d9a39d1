import React, { useState, useContext, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Image,
  Keyboard,
  Animated
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { ThemeContext } from '../contexts/ThemeContext';
import messageService from '../services/messageService';

/**
 * Avatar de l'utilisateur
 */
const UserAvatar = ({ user, size = 36 }) => {
  const { theme } = useContext(ThemeContext);
  const initials = user?.name
    ? user.name
        .split(' ')
        .map(n => n[0])
        .join('')
        .toUpperCase()
        .substring(0, 2)
    : "?";

  return (
    <View
      style={[
        styles.avatar,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: theme.primary + '20',
        },
      ]}
    >
      <Text style={[styles.initials, { color: theme.primary, fontSize: size / 2.5 }]}>
        {initials}
      </Text>
    </View>
  );
};

/**
 * Bulle de message style Messenger
 */
const MessageBubble = ({ message, isLastInGroup, showAvatar, user }) => {
  const { theme, isDarkMode } = useContext(ThemeContext);
  const isMe = message.sender === 'me';

  return (
    <View
      style={[
        styles.messageBubbleContainer,
        isMe ? styles.myMessageContainer : styles.theirMessageContainer,
      ]}
    >
      {!isMe && showAvatar ? (
        <UserAvatar user={user} size={28} />
      ) : (
        <View style={{ width: 28 }} />
      )}

      <View
        style={[
          styles.messageBubble,
          isMe
            ? [styles.myMessage, { backgroundColor: '#0084FF' }]
            : [styles.theirMessage, { backgroundColor: isDarkMode ? '#303030' : '#E4E6EB' }],
        ]}
      >
        <Text
          style={[
            styles.messageText,
            { color: isMe ? 'white' : theme.text.primary },
          ]}
        >
          {message.text}
        </Text>
      </View>

      {isLastInGroup && (
        <Text
          style={[
            styles.messageTime,
            {
              color: theme.text.tertiary,
              alignSelf: 'center',
              marginTop: 4,
            },
          ]}
        >
          {formatTime(message.timestamp)}
        </Text>
      )}
    </View>
  );
};

/**
 * Groupe de messages
 */
const MessageGroup = ({ messages, sender, user }) => {
  return (
    <View style={styles.messageGroup}>
      {messages.map((message, index) => {
        // Déterminer si on doit afficher l'avatar (seulement pour le premier message du groupe)
        const showAvatar = index === 0;

        return (
          <MessageBubble
            key={message.id}
            message={message}
            isLastInGroup={index === messages.length - 1}
            showAvatar={showAvatar}
            user={user}
          />
        );
      })}
    </View>
  );
};

/**
 * Formater l'heure du message (HH:MM)
 */
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

/**
 * Composant de conversation détaillée (similaire à la fenêtre de chat dans Messenger)
 */
const ConversationDetail = ({ conversation, onBack }) => {
  const { theme, isDarkMode } = useContext(ThemeContext);
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [newMessage, setNewMessage] = useState('');
  const [sending, setSending] = useState(false);
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const flatListRef = useRef(null);
  const inputRef = useRef(null);

  // Gérer l'affichage du clavier
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
        if (flatListRef.current && messages.length > 0) {
          setTimeout(() => {
            flatListRef.current.scrollToEnd({ animated: true });
          }, 100);
        }
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, [messages.length]);

  // Charger les messages au chargement du composant
  useEffect(() => {
    const fetchMessages = async () => {
      try {
        setLoading(true);
        const data = await messageService.getMessages(conversation.id);
        setMessages(data);

        // Marquer la conversation comme lue
        await messageService.markConversationAsRead(conversation.id);
      } catch (error) {
        console.error("Erreur lors du chargement des messages:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();
  }, [conversation.id]);

  // Faire défiler vers le bas lorsque de nouveaux messages sont ajoutés
  useEffect(() => {
    if (flatListRef.current && messages.length > 0) {
      setTimeout(() => {
        flatListRef.current.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  // Envoyer un nouveau message
  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    try {
      setSending(true);
      const sentMessage = await messageService.sendMessage(conversation.id, newMessage.trim());
      setMessages([...messages, sentMessage]);
      setNewMessage('');
    } catch (error) {
      console.error("Erreur lors de l'envoi du message:", error);
    } finally {
      setSending(false);
    }
  };

  // Grouper les messages par expéditeur
  const groupMessagesBySender = () => {
    const groups = [];
    let currentGroup = [];
    let currentSender = null;

    messages.forEach((message) => {
      if (currentSender !== message.sender) {
        if (currentGroup.length > 0) {
          groups.push({
            id: currentGroup[0].id,
            sender: currentSender,
            messages: [...currentGroup],
          });
        }
        currentGroup = [message];
        currentSender = message.sender;
      } else {
        currentGroup.push(message);
      }
    });

    if (currentGroup.length > 0) {
      groups.push({
        id: currentGroup[0].id,
        sender: currentSender,
        messages: [...currentGroup],
      });
    }

    return groups;
  };

  const messageGroups = groupMessagesBySender();

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDarkMode ? '#000000' : '#FFFFFF' }]}>
        <ActivityIndicator size="large" color="#0084FF" />
        <Text style={[styles.loadingText, { color: theme.text.secondary }]}>
          Chargement des messages...
        </Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: isDarkMode ? '#000000' : '#FFFFFF' }]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      {/* En-tête de la conversation - Style Messenger */}
      <View style={[styles.header, {
        backgroundColor: isDarkMode ? '#000000' : '#FFFFFF'
      }]}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <MaterialCommunityIcons name="chevron-left" size={28} color={isDarkMode ? '#FFFFFF' : '#0084FF'} />
        </TouchableOpacity>

        <View style={styles.headerInfo}>
          <Text style={[styles.headerName, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {conversation.user.name}
          </Text>
          <Text style={[styles.headerStatus, { color: isDarkMode ? '#CCCCCC' : '#65676B' }]}>
            {conversation.user.status === 'online' ? 'Active now' : 'Offline'}
          </Text>
        </View>
      </View>

      {/* Liste des messages */}
      <FlatList
        ref={flatListRef}
        style={styles.messagesList}
        data={messageGroups}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <MessageGroup
            messages={item.messages}
            sender={item.sender}
            user={item.sender === 'me' ? null : conversation.user}
          />
        )}
        contentContainerStyle={styles.messagesContainer}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons
              name="message-text-outline"
              size={48}
              color={isDarkMode ? '#555555' : '#CCCCCC'}
            />
            <Text style={[styles.emptyText, { color: isDarkMode ? '#FFFFFF' : '#65676B' }]}>
              Aucun message
            </Text>
            <Text style={[styles.emptySubtext, { color: isDarkMode ? '#CCCCCC' : '#8A8D91' }]}>
              Commencez la conversation en envoyant un message
            </Text>
          </View>
        }
      />

      {/* Zone de saisie de message style Messenger - Version simplifiée */}
      <View style={[styles.inputContainer, {
        backgroundColor: isDarkMode ? '#000000' : '#FFFFFF',
      }]}>
        <View style={[styles.inputWrapper, {
          backgroundColor: isDarkMode ? '#333333' : '#F0F2F5',
        }]}>
          <TextInput
            ref={inputRef}
            style={[styles.input, {
              color: isDarkMode ? '#FFFFFF' : '#050505',
            }]}
            placeholder="Aa"
            placeholderTextColor={isDarkMode ? '#AAAAAA' : '#65676B'}
            value={newMessage}
            onChangeText={setNewMessage}
            multiline
          />

          <TouchableOpacity style={styles.emojiButton}>
            <MaterialCommunityIcons name="emoticon-outline" size={24} color={isDarkMode ? '#FFFFFF' : '#0084FF'} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={styles.sendButton}
          onPress={handleSendMessage}
          disabled={!newMessage.trim() || sending}
        >
          {sending ? (
            <ActivityIndicator size="small" color={isDarkMode ? '#FFFFFF' : '#0084FF'} />
          ) : (
            <MaterialCommunityIcons
              name="thumb-up"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#0084FF'}
            />
          )}
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
  },
  headerInfo: {
    flex: 1,
    marginLeft: 12,
  },
  headerName: {
    fontSize: 16,
    fontWeight: '600',
  },
  headerStatus: {
    fontSize: 12,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerAction: {
    padding: 8,
    marginLeft: 4,
  },
  messagesList: {
    flex: 1,
  },
  messagesContainer: {
    padding: 10,
    flexGrow: 1,
  },
  messageGroup: {
    marginBottom: 8,
  },
  messageBubbleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 2,
    maxWidth: '80%',
  },
  myMessageContainer: {
    alignSelf: 'flex-end',
    marginLeft: 50,
  },
  theirMessageContainer: {
    alignSelf: 'flex-start',
    marginRight: 50,
  },
  messageBubble: {
    borderRadius: 18,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginHorizontal: 4,
  },
  myMessage: {
    borderBottomRightRadius: 4,
  },
  theirMessage: {
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
  },
  messageTime: {
    fontSize: 11,
    marginTop: 2,
    marginHorizontal: 4,
  },
  avatar: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 4,
  },
  initials: {
    fontWeight: 'bold',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flex: 1,
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontSize: 16,
    maxHeight: 100,
    padding: 0,
    minHeight: 24,
  },
  emojiButton: {
    marginLeft: 8,
  },
  sendButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '500',
  },
  emptySubtext: {
    marginTop: 8,
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
});

export default ConversationDetail;
