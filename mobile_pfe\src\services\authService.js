import api from "./api";
import { API_URL } from "../config/api";

const authService = {
  // Test API connection
  testConnection: async () => {
    try {
      // Try multiple endpoints in case one is not available
      let endpoints = [
        "/", // Root endpoint - most likely to work
        "/api", // Try with /api if the baseURL doesn't include it
        "/api/test", // Common test endpoint
        "/mobile/test", // Our dedicated mobile test endpoint
        "/chatbot/test-endpoint", // Fallback to chatbot test endpoint
      ];

      let lastError = null;
      let allErrors = [];

      // Try each endpoint until one works
      for (const endpoint of endpoints) {
        try {
          console.log(`Trying to connect to endpoint: ${endpoint}`);
          // Set a shorter timeout for this specific request
          const response = await api.get(endpoint, { timeout: 5000 });
          return {
            success: true,
            data: response.data,
            message: "Successfully connected to API server",
            endpoint: endpoint,
            apiUrl: API_URL,
          };
        } catch (error) {
          console.log(`Failed to connect to ${endpoint}:`, error.message);

          // Store detailed error info for debugging
          allErrors.push({
            endpoint,
            message: error.message,
            code: error.code,
            status: error.response?.status,
            statusText: error.response?.statusText,
          });

          lastError = error;
          // Continue to the next endpoint
        }
      }

      // If we get here, all endpoints failed
      throw { ...lastError, allErrors };
    } catch (error) {
      console.error("API connection test error:", error);

      // Create a more user-friendly error message
      let errorMessage = "Could not connect to server";

      if (error.code === "ECONNABORTED") {
        errorMessage =
          "Connection timeout. The server took too long to respond.";
      } else if (error.code === "ERR_NETWORK") {
        errorMessage =
          "Network error. Please check your internet connection and server status.";
      } else if (error.message && error.message.includes("SSL")) {
        errorMessage =
          "SSL Error: Your server might be using HTTP instead of HTTPS. Try changing the API URL.";
      } else if (error.response) {
        // The server responded with a status code outside the 2xx range
        errorMessage = `Server error: ${error.response.status} ${error.response.statusText}`;
      }

      return {
        success: false,
        error: errorMessage,
        details: error,
        allErrors: error.allErrors || [],
        message: "Failed to connect to API server",
        apiUrl: API_URL,
      };
    }
  },

  // Login user
  login: async (email, password) => {
    try {
      console.log(`Attempting to login with email: ${email}`);

      // Try different login endpoints
      const loginEndpoints = [
        "/login", // Standard Laravel/Sanctum endpoint
        "/api/login", // If the baseURL doesn't include /api
        "/auth/login", // Common auth endpoint
        "/api/auth/login", // Another common pattern
      ];

      let lastError = null;

      // Try each endpoint until one works
      for (const endpoint of loginEndpoints) {
        try {
          console.log(`Trying to login with endpoint: ${endpoint}`);
          const response = await api.post(endpoint, { email, password });

          if (response.data && response.data.token) {
            console.log(`Login successful with endpoint: ${endpoint}`);
            return response.data;
          } else {
            console.log(
              `Login endpoint ${endpoint} returned invalid response:`,
              response.data
            );
          }
        } catch (err) {
          console.log(`Login failed with endpoint ${endpoint}:`, err.message);
          lastError = err;
          // Continue to next endpoint
        }
      }

      // If we get here, all endpoints failed
      throw lastError || new Error("All login endpoints failed");
    } catch (error) {
      console.error("Login error:", error);

      // Network errors
      if (error.message === "Network Error" || error.code === "ERR_NETWORK") {
        console.error("Network error details:", error);
        throw new Error(
          "Erreur réseau: Impossible de se connecter au serveur. Vérifiez votre connexion internet et l'état du serveur."
        );
      }

      // Timeout errors
      else if (error.code === "ECONNABORTED") {
        throw new Error(
          "Le serveur a mis trop de temps à répondre. Veuillez réessayer plus tard."
        );
      }

      // Server responded with an error
      else if (error.response) {
        // Handle specific error responses
        if (error.response.status === 401) {
          throw new Error("Email ou mot de passe incorrect");
        } else if (error.response.status === 403) {
          throw new Error("Votre compte est en attente d'approbation");
        } else if (error.response.status >= 500) {
          throw new Error(
            "Erreur serveur: Le serveur a rencontré un problème. Veuillez réessayer plus tard."
          );
        } else if (error.response.data && error.response.data.message) {
          throw new Error(error.response.data.message);
        }
      }

      // Default error message
      throw new Error(
        "Impossible de se connecter au serveur. Veuillez réessayer plus tard."
      );
    }
  },

  // Register user
  register: async (userData) => {
    try {
      const response = await api.post("/register", userData);
      return response.data;
    } catch (error) {
      console.error("Registration error:", error);

      if (error.response) {
        // Handle specific error responses
        if (error.response.status === 400 && error.response.data.message) {
          throw new Error(error.response.data.message);
        } else if (error.response.data && error.response.data.errors) {
          // Handle validation errors
          const errorMessages = Object.values(
            error.response.data.errors
          ).flat();
          throw new Error(errorMessages.join(", "));
        }
      }

      throw new Error("Impossible de créer votre compte");
    }
  },

  // Logout user
  logout: async () => {
    try {
      await api.post("/logout");
      return true;
    } catch (error) {
      console.error("Logout error:", error);
      // Even if the server-side logout fails, we still want to clear local storage
      return true;
    }
  },

  // Verify token is still valid
  verifyToken: async () => {
    try {
      const response = await api.get("/user/me");
      return !!response.data && !!response.data.user;
    } catch (error) {
      console.error("Token verification error:", error);
      return false;
    }
  },

  // Request password reset
  forgotPassword: async (email) => {
    try {
      const response = await api.post("/forgot-password", { email });
      return response.data;
    } catch (error) {
      console.error("Password reset request error:", error);

      if (
        error.response &&
        error.response.data &&
        error.response.data.message
      ) {
        throw new Error(error.response.data.message);
      }

      throw new Error(
        "Impossible de traiter votre demande de réinitialisation de mot de passe"
      );
    }
  },

  // Reset password with token
  resetPassword: async (token, password, passwordConfirmation) => {
    try {
      const response = await api.post(`/reset-password/${token}`, {
        password,
        password_confirmation: passwordConfirmation,
      });
      return response.data;
    } catch (error) {
      console.error("Password reset error:", error);

      if (
        error.response &&
        error.response.data &&
        error.response.data.message
      ) {
        throw new Error(error.response.data.message);
      }

      throw new Error("Impossible de réinitialiser votre mot de passe");
    }
  },

  // Get current user profile
  getUserProfile: async () => {
    try {
      const response = await api.get("/user/me");
      return response.data.user;
    } catch (error) {
      console.error("Get user profile error:", error);
      throw new Error("Impossible de récupérer votre profil");
    }
  },

  // Update user profile
  updateUserProfile: async (userId, userData) => {
    try {
      const response = await api.put(`/user/${userId}`, userData);
      return response.data;
    } catch (error) {
      console.error("Update profile error:", error);

      if (
        error.response &&
        error.response.data &&
        error.response.data.message
      ) {
        throw new Error(error.response.data.message);
      }

      throw new Error("Impossible de mettre à jour votre profil");
    }
  },
};

export default authService;
