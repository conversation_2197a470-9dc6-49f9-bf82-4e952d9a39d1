import React, { useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { ThemeContext } from '../contexts/ThemeContext';
import messageService from '../services/messageService';

/**
 * Composant d'avatar pour les utilisateurs
 */
const UserAvatar = ({ user, size = 50, showStatus = true }) => {
  const { theme } = useContext(ThemeContext);
  const initials = user.name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);

  return (
    <View style={{ position: 'relative' }}>
      <View
        style={[
          styles.avatar,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            backgroundColor: theme.primary + '30',
          },
        ]}
      >
        <Text style={[styles.initials, { color: theme.primary, fontSize: size / 2.5 }]}>
          {initials}
        </Text>
      </View>
      {showStatus && user.status === 'online' && (
        <View
          style={[
            styles.statusIndicator,
            {
              width: size / 4,
              height: size / 4,
              borderRadius: size / 8,
              backgroundColor: theme.success,
              borderColor: theme.card,
            },
          ]}
        />
      )}
    </View>
  );
};

/**
 * Élément de conversation pour la liste
 */
const ConversationItem = ({ conversation, onPress }) => {
  const { theme } = useContext(ThemeContext);
  const { user, lastMessage, unreadCount } = conversation;

  return (
    <TouchableOpacity
      style={[
        styles.conversationItem,
        {
          backgroundColor: unreadCount > 0 ? theme.primary + '10' : 'transparent',
          borderBottomColor: theme.border,
        },
      ]}
      onPress={() => onPress(conversation)}
    >
      <UserAvatar user={user} />
      
      <View style={styles.conversationContent}>
        <View style={styles.conversationHeader}>
          <Text
            style={[
              styles.userName,
              {
                color: theme.text.primary,
                fontWeight: unreadCount > 0 ? '700' : '500',
              },
            ]}
            numberOfLines={1}
          >
            {user.name}
          </Text>
          <Text style={[styles.messageTime, { color: theme.text.tertiary }]}>
            {messageService.formatMessageTime(lastMessage.timestamp)}
          </Text>
        </View>
        
        <View style={styles.messagePreviewContainer}>
          <Text
            style={[
              styles.messagePreview,
              {
                color: unreadCount > 0 ? theme.text.primary : theme.text.secondary,
                fontWeight: unreadCount > 0 ? '500' : 'normal',
              },
            ]}
            numberOfLines={1}
          >
            {lastMessage.sender === 'me' ? 'Vous: ' : ''}
            {lastMessage.text}
          </Text>
          
          {unreadCount > 0 && (
            <View style={[styles.unreadBadge, { backgroundColor: theme.primary }]}>
              <Text style={styles.unreadCount}>{unreadCount}</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

/**
 * Liste des conversations (similaire à la liste des contacts dans Messenger)
 */
const ConversationList = ({ conversations, loading, onConversationPress, onNewConversation }) => {
  const { theme } = useContext(ThemeContext);

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.background }]}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={[styles.loadingText, { color: theme.text.secondary }]}>
          Chargement des conversations...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <FlatList
        data={conversations}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <ConversationItem conversation={item} onPress={onConversationPress} />
        )}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons
              name="message-text-outline"
              size={48}
              color={theme.text.tertiary}
            />
            <Text style={[styles.emptyText, { color: theme.text.secondary }]}>
              Aucune conversation
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.text.tertiary }]}>
              Commencez à discuter avec vos formateurs et camarades
            </Text>
          </View>
        }
      />
      
      <TouchableOpacity
        style={[styles.newConversationButton, { backgroundColor: theme.primary }]}
        onPress={onNewConversation}
      >
        <MaterialCommunityIcons name="message-plus" size={24} color="white" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  conversationItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  avatar: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  initials: {
    fontWeight: 'bold',
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    borderWidth: 2,
  },
  conversationContent: {
    flex: 1,
    marginLeft: 12,
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 16,
    flex: 1,
  },
  messageTime: {
    fontSize: 12,
    marginLeft: 8,
  },
  messagePreviewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  messagePreview: {
    fontSize: 14,
    flex: 1,
  },
  unreadBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    paddingHorizontal: 4,
  },
  unreadCount: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '500',
  },
  emptySubtext: {
    marginTop: 8,
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  newConversationButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});

export default ConversationList;
