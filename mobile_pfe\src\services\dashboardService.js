import api from './api';

const dashboardService = {
  // Get dashboard statistics - real API implementation
  getDashboardStats: async () => {
    try {
      console.log('Getting dashboard stats from real API');

      // Use the same endpoint as frontend_pfe
      const response = await api.get('/dashboard/stats');

      console.log('Dashboard stats API response:', response.data);

      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      // Return default stats if API fails
      return {
        coursesInProgress: 0,
        completedQuizzes: 0,
        certifications: 0,
        overallProgress: 0,
        courseProgress: [],
        recentActivities: []
      };
    }
  },

  // Get recent activities - real API implementation
  getRecentActivities: async (limit = 10) => {
    try {
      console.log('Getting recent activities from real API');

      // Use the same endpoint pattern as frontend_pfe
      const response = await api.get('/dashboard/activities', {
        params: { limit }
      });

      console.log('Recent activities API response:', response.data);

      // Handle different response formats
      const activities = response.data?.['hydra:member'] || response.data || [];

      return Array.isArray(activities) ? activities : [];
    } catch (error) {
      console.error('Error fetching recent activities:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      return [];
    }
  },

  // Get course progress summary - real API implementation
  getCourseProgressSummary: async () => {
    try {
      console.log('Getting course progress summary from real API');

      // Use the same endpoint pattern as frontend_pfe
      const response = await api.get('/dashboard/course-progress');

      console.log('Course progress summary API response:', response.data);

      // Handle different response formats
      const progress = response.data?.['hydra:member'] || response.data || [];

      return Array.isArray(progress) ? progress : [];
    } catch (error) {
      console.error('Error fetching course progress summary:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      return [];
    }
  },

  // Get upcoming deadlines - real API implementation
  getUpcomingDeadlines: async () => {
    try {
      console.log('Getting upcoming deadlines from real API');

      // Use the same endpoint pattern as frontend_pfe
      const response = await api.get('/dashboard/deadlines');

      console.log('Upcoming deadlines API response:', response.data);

      // Handle different response formats
      const deadlines = response.data?.['hydra:member'] || response.data || [];

      return Array.isArray(deadlines) ? deadlines : [];
    } catch (error) {
      console.error('Error fetching upcoming deadlines:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      return [];
    }
  },

  // Get performance metrics - real API implementation
  getPerformanceMetrics: async () => {
    try {
      console.log('Getting performance metrics from real API');

      // Use the same endpoint pattern as frontend_pfe
      const response = await api.get('/dashboard/performance');

      console.log('Performance metrics API response:', response.data);

      return response.data || {
        averageScore: 0,
        completionRate: 0,
        timeSpent: 0,
        streak: 0
      };
    } catch (error) {
      console.error('Error fetching performance metrics:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      return {
        averageScore: 0,
        completionRate: 0,
        timeSpent: 0,
        streak: 0
      };
    }
  },

  // Get all dashboard data in one call - matching frontend_pfe approach
  getAllDashboardData: async () => {
    try {
      console.log('Getting all dashboard data from real API');

      // Use the same approach as frontend_pfe Dashboard component
      // Get courses data first, then calculate stats from it
      const coursesResponse = await api.get('/apprenant/cours');
      const coursData = coursesResponse.data?.cours || coursesResponse.data || [];

      console.log('Dashboard courses data:', coursData);

      // Calculate stats from courses data (same logic as frontend_pfe)
      let coursesInProgress = 0;
      let completedQuizzes = 0;
      let certifications = 0;
      let totalProgress = 0;

      const courseProgress = [];
      const recentActivities = [];

      if (Array.isArray(coursData) && coursData.length > 0) {
        coursData.forEach(course => {
          // Count courses in progress
          if (course.status !== 'completed') {
            coursesInProgress++;
          }

          // Count completed quizzes
          if (course.quizzes) {
            completedQuizzes += course.quizzes.filter(quiz => quiz.completed).length;
          }

          // Count certifications
          if (course.certificate) {
            certifications++;
          }

          // Calculate progress
          const progress = course.progress || 0;
          totalProgress += progress;

          // Add to course progress array
          courseProgress.push({
            id: course.id,
            title: course.title || course.nom,
            progress: progress,
            status: course.status || 'in-progress'
          });

          // Add recent activities
          if (course.lastActivity) {
            recentActivities.push({
              title: `Activité dans ${course.title || course.nom}`,
              time: course.lastActivity,
              type: 'course',
              status: 'completed'
            });
          }
        });
      }

      const overallProgress = coursData.length > 0 ? Math.round(totalProgress / coursData.length) : 0;

      // Add default activity if no courses
      if (recentActivities.length === 0) {
        recentActivities.push({
          title: 'Aucune activité récente',
          time: 'Maintenant',
          type: 'info',
          status: 'pending'
        });
      }

      const dashboardData = {
        stats: {
          coursesInProgress,
          completedQuizzes,
          certifications,
          overallProgress
        },
        courseProgress,
        activities: recentActivities.slice(0, 5), // Limit to 5 recent activities
        deadlines: [], // TODO: Implement deadlines from backend
        performance: {
          averageScore: overallProgress,
          completionRate: overallProgress,
          timeSpent: coursData.length * 2, // Estimate
          streak: 0 // TODO: Implement streak calculation
        }
      };

      console.log('Processed dashboard data:', dashboardData);
      return dashboardData;

    } catch (error) {
      console.error('Error fetching all dashboard data:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      // Return empty data if API fails
      return {
        stats: {
          coursesInProgress: 0,
          completedQuizzes: 0,
          certifications: 0,
          overallProgress: 0
        },
        activities: [{
          title: 'Erreur de chargement des données',
          time: 'Maintenant',
          type: 'error',
          status: 'error'
        }],
        courseProgress: [],
        deadlines: [],
        performance: {
          averageScore: 0,
          completionRate: 0,
          timeSpent: 0,
          streak: 0
        }
      };
    }
  }
};

export default dashboardService;
