import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { AuthContext } from '../../contexts/AuthContext';
import coursService from '../../services/coursService';
import CourseCard from '../../components/CourseCard';
import LoadingScreen from '../../components/LoadingScreen';

const CoursesScreen = ({ navigation }) => {
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [courses, setCourses] = useState([]);
  const [filteredCourses, setFilteredCourses] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState(null);
  const [activeFilter, setActiveFilter] = useState('all');

  const fetchCourses = async () => {
    try {
      setError(null);
      const coursesData = await coursService.getApprenantCours();

      // Add default values for missing properties
      const processedCourses = coursesData.map(course => ({
        ...course,
        titre: course.titre || course.title || 'Cours sans titre',
        progress: course.progress || 0,
        description: course.description || 'Aucune description disponible',
        image: course.image || null,
      }));

      setCourses(processedCourses);
      setFilteredCourses(processedCourses);
    } catch (err) {
      console.error('Error fetching courses:', err);
      setError(err.message || 'Impossible de charger les cours');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchCourses();
    setRefreshing(false);
  };

  useEffect(() => {
    const loadCourses = async () => {
      setLoading(true);
      await fetchCourses();
      setLoading(false);
    };

    loadCourses();
  }, []);

  useEffect(() => {
    if (courses.length > 0) {
      let filtered = [...courses];

      // Apply search filter
      if (searchQuery) {
        filtered = filtered.filter(course =>
          (course.titre || course.title || '')
            .toLowerCase()
            .includes(searchQuery.toLowerCase())
        );
      }

      // Apply category filter
      if (activeFilter !== 'all') {
        if (activeFilter === 'in-progress') {
          filtered = filtered.filter(course =>
            course.progress && course.progress > 0 && course.progress < 100
          );
        } else if (activeFilter === 'completed') {
          filtered = filtered.filter(course =>
            course.progress && course.progress === 100
          );
        } else if (activeFilter === 'not-started') {
          filtered = filtered.filter(course =>
            !course.progress || course.progress === 0
          );
        }
      }

      setFilteredCourses(filtered);
    }
  }, [searchQuery, activeFilter, courses]);

  const handleCoursePress = (course) => {
    navigation.navigate('CourseDetail', {
      courseId: course.id,
      title: course.titre || course.title
    });
  };

  if (loading) {
    return <LoadingScreen message="Chargement des cours..." />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <MaterialCommunityIcons name="magnify" size={24} color="#6B7280" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher un cours..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <MaterialCommunityIcons name="close" size={20} color="#6B7280" />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>

      <View style={styles.filtersContainer}>
        <TouchableOpacity
          style={[styles.filterButton, activeFilter === 'all' && styles.activeFilterButton]}
          onPress={() => setActiveFilter('all')}
        >
          <Text style={[styles.filterText, activeFilter === 'all' && styles.activeFilterText]}>
            Tous
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterButton, activeFilter === 'in-progress' && styles.activeFilterButton]}
          onPress={() => setActiveFilter('in-progress')}
        >
          <Text style={[styles.filterText, activeFilter === 'in-progress' && styles.activeFilterText]}>
            En cours
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterButton, activeFilter === 'completed' && styles.activeFilterButton]}
          onPress={() => setActiveFilter('completed')}
        >
          <Text style={[styles.filterText, activeFilter === 'completed' && styles.activeFilterText]}>
            Terminés
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterButton, activeFilter === 'not-started' && styles.activeFilterButton]}
          onPress={() => setActiveFilter('not-started')}
        >
          <Text style={[styles.filterText, activeFilter === 'not-started' && styles.activeFilterText]}>
            Non commencés
          </Text>
        </TouchableOpacity>
      </View>

      {error ? (
        <View style={styles.errorContainer}>
          <MaterialCommunityIcons name="alert-circle" size={48} color="#EF4444" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={onRefresh}>
            <Text style={styles.retryButtonText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={filteredCourses}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <CourseCard course={item} onPress={handleCoursePress} />
          )}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <MaterialCommunityIcons name="book-open-variant" size={48} color="#6B7280" />
              <Text style={styles.emptyText}>
                {searchQuery
                  ? 'Aucun cours ne correspond à votre recherche'
                  : 'Aucun cours disponible'}
              </Text>
              <Text style={styles.emptySubtext}>
                {searchQuery
                  ? 'Essayez avec un autre terme de recherche'
                  : 'Les cours assignés apparaîtront ici'}
              </Text>
            </View>
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: '#1F2937',
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
  },
  activeFilterButton: {
    backgroundColor: '#4F46E5',
  },
  filterText: {
    fontSize: 14,
    color: '#6B7280',
  },
  activeFilterText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  listContent: {
    padding: 16,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    marginTop: 48,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#4F46E5',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default CoursesScreen;
