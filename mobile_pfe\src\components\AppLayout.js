import React, { useContext } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  KeyboardAvoidingView,
  Platform,
  StatusBar
} from 'react-native';
import { ThemeContext } from '../contexts/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AppHeader from './AppHeader';

const AppLayout = ({
  children,
  title,
  showBackButton = false,
  showHeader = true,
  showProfileButton = true,
  scrollable = true,
  refreshing = false,
  onRefresh = null,
  style,
  contentContainerStyle,
  keyboardAvoiding = false
}) => {
  const { theme, isDarkMode } = useContext(ThemeContext);
  const insets = useSafeAreaInsets();

  const renderContent = () => {
    const content = (
      <View
        style={[
          styles.contentContainer,
          { paddingBottom: insets.bottom },
          contentContainerStyle
        ]}
      >
        {children}
      </View>
    );

    if (scrollable) {
      return (
        <ScrollView
          style={[styles.scrollView, style]}
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            onRefresh ? (
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[theme.primary]}
                tintColor={theme.primary}
                progressBackgroundColor={theme.card}
              />
            ) : undefined
          }
        >
          {content}
        </ScrollView>
      );
    }

    return (
      <View style={[styles.container, style]}>
        {content}
      </View>
    );
  };

  const mainContent = (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={theme.card}
      />

      {showHeader && (
        <AppHeader
          title={title}
          showBackButton={showBackButton}
          showProfileButton={showProfileButton}
        />
      )}

      {renderContent()}
    </View>
  );

  if (keyboardAvoiding) {
    return (
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
      >
        {mainContent}
      </KeyboardAvoidingView>
    );
  }

  return mainContent;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    zIndex: 1, // Lower z-index to ensure it doesn't overlap with the profile dropdown
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  contentContainer: {
    flex: 1,
    padding: 16,
    zIndex: 1, // Lower z-index to ensure it doesn't overlap with the profile dropdown
  },
});

export default AppLayout;
