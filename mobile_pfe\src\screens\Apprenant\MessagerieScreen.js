import React, { useState, useEffect, useContext, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { AuthContext } from '../../contexts/AuthContext';
import { ThemeContext } from '../../contexts/ThemeContext';
import messagerieService from '../../services/messagerieService';
import MessageItem from '../../components/MessageItem';
import LoadingScreen from '../../components/LoadingScreen';

const ConversationItem = ({ conversation, isActive, onPress }) => {
  const { theme, isDarkMode } = useContext(ThemeContext);

  return (
    <TouchableOpacity
      style={[
        styles.conversationItem,
        {
          borderBottomColor: theme.border,
          backgroundColor: isActive ? (isDarkMode ? `${theme.primary}20` : '#EEF2FF') : theme.card
        }
      ]}
      onPress={onPress}
    >
      <Image
        source={
          conversation.avatar
            ? { uri: conversation.avatar }
            : require('../../../assets/default-avatar.png')
        }
        style={styles.conversationAvatar}
      />

      <View style={styles.conversationContent}>
        <Text style={[styles.conversationName, { color: theme.text.primary }]}>{conversation.name}</Text>
        <Text
          style={[styles.conversationLastMessage, { color: theme.text.secondary }]}
          numberOfLines={1}
        >
          {conversation.lastMessage || 'Aucun message'}
        </Text>
      </View>

      <View style={styles.conversationMeta}>
        <Text style={[styles.conversationTime, { color: theme.text.tertiary }]}>{conversation.time}</Text>
        {conversation.unread > 0 && (
          <View style={[styles.unreadBadge, { backgroundColor: theme.primary }]}>
            <Text style={[styles.unreadText, { color: theme.text.inverse }]}>{conversation.unread}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const MessagerieScreen = () => {
  const { user } = useContext(AuthContext);
  const { theme, isDarkMode } = useContext(ThemeContext);
  const [loading, setLoading] = useState(true);
  const [conversations, setConversations] = useState([]);
  const [messages, setMessages] = useState([]);
  const [activeConversation, setActiveConversation] = useState(null);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const [error, setError] = useState(null);

  const messagesListRef = useRef(null);

  const fetchConversations = async () => {
    try {
      setError(null);
      const conversationsData = await messagerieService.getConversations();
      setConversations(conversationsData);

      // Set first conversation as active if none is selected
      if (conversationsData.length > 0 && !activeConversation) {
        setActiveConversation(conversationsData[0]);
        fetchMessages(conversationsData[0].id);
      }
    } catch (err) {
      console.error('Error fetching conversations:', err);
      setError('Impossible de charger les conversations');
    }
  };

  const fetchMessages = async (formateurId) => {
    try {
      const messagesData = await messagerieService.getMessages(formateurId);
      setMessages(messagesData);

      // Mark messages as read
      const unreadMessageIds = messagesData
        .filter(msg => !msg.isRead && !msg.isMe)
        .map(msg => msg.id);

      if (unreadMessageIds.length > 0) {
        await messagerieService.markAsRead(unreadMessageIds);

        // Update conversations list to reflect read messages
        setConversations(prevConversations =>
          prevConversations.map(conv =>
            conv.id === formateurId
              ? { ...conv, unread: 0 }
              : conv
          )
        );
      }
    } catch (err) {
      console.error('Error fetching messages:', err);
      Alert.alert('Erreur', 'Impossible de charger les messages');
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !activeConversation) return;

    try {
      setSendingMessage(true);

      await messagerieService.sendMessage(activeConversation.id, newMessage.trim());

      // Clear input
      setNewMessage('');

      // Refresh messages
      await fetchMessages(activeConversation.id);

      // Update conversation list
      await fetchConversations();

      // Scroll to bottom
      if (messagesListRef.current) {
        messagesListRef.current.scrollToEnd({ animated: true });
      }
    } catch (err) {
      console.error('Error sending message:', err);
      Alert.alert('Erreur', 'Impossible d\'envoyer le message');
    } finally {
      setSendingMessage(false);
    }
  };

  const handleConversationPress = (conversation) => {
    setActiveConversation(conversation);
    fetchMessages(conversation.id);
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchConversations();
      setLoading(false);
    };

    loadData();

    // Refresh conversations every 30 seconds
    const intervalId = setInterval(() => {
      fetchConversations();
      if (activeConversation) {
        fetchMessages(activeConversation.id);
      }
    }, 30000);

    return () => clearInterval(intervalId);
  }, []);

  if (loading) {
    return <LoadingScreen message="Chargement de la messagerie..." />;
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[styles.container, { backgroundColor: theme.background }]}
      keyboardVerticalOffset={80}
    >
      <View style={[styles.header, { backgroundColor: theme.card, borderBottomColor: theme.border }]}>
        <Text style={[styles.headerTitle, { color: theme.text.primary }]}>Messagerie</Text>
      </View>

      {error ? (
        <View style={styles.errorContainer}>
          <MaterialCommunityIcons name="alert-circle" size={48} color={theme.danger} />
          <Text style={[styles.errorText, { color: theme.text.primary }]}>{error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.primary }]}
            onPress={fetchConversations}
          >
            <Text style={[styles.retryButtonText, { color: theme.text.inverse }]}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.content}>
          {/* Conversations List */}
          <View style={[styles.conversationsContainer, {
            borderRightColor: theme.border,
            backgroundColor: theme.card
          }]}>
            <FlatList
              data={conversations}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item }) => (
                <ConversationItem
                  conversation={item}
                  isActive={activeConversation && activeConversation.id === item.id}
                  onPress={() => handleConversationPress(item)}
                />
              )}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={[styles.emptyText, { color: theme.text.secondary }]}>Aucune conversation</Text>
                </View>
              }
            />
          </View>

          {/* Messages */}
          <View style={[styles.messagesContainer, { backgroundColor: theme.card }]}>
            {activeConversation ? (
              <>
                <View style={[styles.messagesHeader, {
                  borderBottomColor: theme.border,
                  backgroundColor: theme.card
                }]}>
                  <Image
                    source={
                      activeConversation.avatar
                        ? { uri: activeConversation.avatar }
                        : require('../../../assets/default-avatar.png')
                    }
                    style={styles.activeConversationAvatar}
                  />
                  <Text style={[styles.activeConversationName, { color: theme.text.primary }]}>
                    {activeConversation.name}
                  </Text>
                </View>

                <FlatList
                  ref={messagesListRef}
                  data={messages}
                  keyExtractor={(item) => item.id.toString()}
                  renderItem={({ item, index }) => {
                    // Check if previous message is from the same sender
                    const prevMsg = index > 0 ? messages[index - 1] : null;
                    const showAvatar = !item.isMe && (!prevMsg || prevMsg.isMe !== item.isMe);

                    return (
                      <MessageItem
                        message={item}
                        isMe={item.isMe}
                        showAvatar={showAvatar}
                        time={item.time}
                        isFile={item.type === 'file'}
                        onFilePress={() => {
                          // Handle file press
                          Alert.alert('Info', 'Téléchargement de fichier à implémenter');
                        }}
                      />
                    );
                  }}
                  contentContainerStyle={styles.messagesList}
                  ListEmptyComponent={
                    <View style={styles.emptyMessagesContainer}>
                      <MaterialCommunityIcons name="message-text" size={48} color={theme.text.tertiary} />
                      <Text style={[styles.emptyMessagesText, { color: theme.text.primary }]}>
                        Aucun message dans cette conversation
                      </Text>
                      <Text style={[styles.emptyMessagesSubtext, { color: theme.text.secondary }]}>
                        Envoyez un message pour commencer la conversation
                      </Text>
                    </View>
                  }
                  onContentSizeChange={() =>
                    messagesListRef.current?.scrollToEnd({ animated: true })
                  }
                />

                <View style={[styles.inputContainer, {
                  borderTopColor: theme.border,
                  backgroundColor: theme.card
                }]}>
                  <TextInput
                    style={[styles.input, {
                      backgroundColor: isDarkMode ? theme.background : '#F3F4F6',
                      color: theme.text.primary
                    }]}
                    placeholder="Écrivez votre message..."
                    placeholderTextColor={theme.text.tertiary}
                    value={newMessage}
                    onChangeText={setNewMessage}
                    multiline
                  />
                  <TouchableOpacity
                    style={[
                      styles.sendButton,
                      { backgroundColor: theme.primary },
                      (!newMessage.trim() || sendingMessage) && [styles.sendButtonDisabled, { backgroundColor: isDarkMode ? '#6366F180' : '#A5B4FC' }]
                    ]}
                    onPress={handleSendMessage}
                    disabled={!newMessage.trim() || sendingMessage}
                  >
                    {sendingMessage ? (
                      <ActivityIndicator size="small" color={theme.text.inverse} />
                    ) : (
                      <MaterialCommunityIcons name="send" size={20} color={theme.text.inverse} />
                    )}
                  </TouchableOpacity>
                </View>
              </>
            ) : (
              <View style={styles.noConversationContainer}>
                <MaterialCommunityIcons name="message-text" size={48} color={theme.text.tertiary} />
                <Text style={[styles.noConversationText, { color: theme.text.primary }]}>
                  Sélectionnez une conversation
                </Text>
                <Text style={[styles.noConversationSubtext, { color: theme.text.secondary }]}>
                  Choisissez un formateur dans la liste pour voir vos messages
                </Text>
              </View>
            )}
          </View>
        </View>
      )}
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  conversationsContainer: {
    width: '30%',
    borderRightWidth: 1,
  },
  conversationItem: {
    flexDirection: 'row',
    padding: 12,
    borderBottomWidth: 1,
  },
  conversationAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  conversationContent: {
    flex: 1,
    justifyContent: 'center',
  },
  conversationName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  conversationLastMessage: {
    fontSize: 12,
  },
  conversationMeta: {
    alignItems: 'flex-end',
  },
  conversationTime: {
    fontSize: 10,
    marginBottom: 4,
  },
  unreadBadge: {
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unreadText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  messagesContainer: {
    flex: 1,
  },
  messagesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
  },
  activeConversationAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 12,
  },
  activeConversationName: {
    fontSize: 16,
    fontWeight: '600',
  },
  messagesList: {
    padding: 16,
    flexGrow: 1,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 12,
    borderTopWidth: 1,
  },
  input: {
    flex: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    maxHeight: 100,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  sendButtonDisabled: {
    opacity: 0.7,
  },
  emptyContainer: {
    padding: 16,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
  },
  emptyMessagesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyMessagesText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyMessagesSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  noConversationContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  noConversationText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  noConversationSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default MessagerieScreen;