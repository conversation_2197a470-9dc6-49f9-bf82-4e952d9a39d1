import api from './api';

/**
 * Service de messagerie pour gérer les conversations et les messages
 * Utilise des données simulées pour la démo
 */

// Données simulées pour les conversations
const mockConversations = [
  {
    id: 1,
    user: {
      id: 101,
      name: 'Dr. <PERSON>',
      role: 'Formateur',
      avatar: null,
      status: 'online'
    },
    lastMessage: {
      id: 1001,
      text: 'Bon<PERSON><PERSON>, avez-vous terminé le devoir pour le module JavaScript?',
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
      read: false,
      sender: 101
    },
    unreadCount: 2
  },
  {
    id: 2,
    user: {
      id: 102,
      name: '<PERSON><PERSON><PERSON>',
      role: 'Responsable Formation',
      avatar: null,
      status: 'offline'
    },
    lastMessage: {
      id: 2001,
      text: 'Votre demande de certificat a été approuvée.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), // 3 hours ago
      read: true,
      sender: 102
    },
    unreadCount: 0
  },
  {
    id: 3,
    user: {
      id: 103,
      name: 'Prof<PERSON>',
      role: 'Formateur',
      avatar: null,
      status: 'online'
    },
    lastMessage: {
      id: 3001,
      text: 'Le prochain cours aura lieu mardi à 14h. N\'oubliez pas de préparer vos questions.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
      read: true,
      sender: 103
    },
    unreadCount: 0
  },
  {
    id: 4,
    user: {
      id: 104,
      name: 'Support Technique',
      role: 'Support',
      avatar: null,
      status: 'online'
    },
    lastMessage: {
      id: 4001,
      text: 'Votre problème a été résolu. N\'hésitez pas à nous contacter si vous avez d\'autres questions.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(), // 2 days ago
      read: true,
      sender: 104
    },
    unreadCount: 0
  },
  {
    id: 5,
    user: {
      id: 105,
      name: 'Karim Idrissi',
      role: 'Apprenant',
      avatar: null,
      status: 'offline'
    },
    lastMessage: {
      id: 5001,
      text: 'Est-ce que tu as les notes du cours d\'hier?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 72).toISOString(), // 3 days ago
      read: true,
      sender: 105
    },
    unreadCount: 0
  }
];

// Données simulées pour les messages
const mockMessages = {
  1: [
    {
      id: 1001,
      text: 'Bonjour, avez-vous terminé le devoir pour le module JavaScript?',
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
      sender: 101,
      read: false
    },
    {
      id: 1002,
      text: 'N\'oubliez pas que la date limite est demain à midi.',
      timestamp: new Date(Date.now() - 1000 * 60 * 29).toISOString(),
      sender: 101,
      read: false
    }
  ],
  2: [
    {
      id: 2001,
      text: 'Bonjour, je vous informe que votre demande de certificat a été examinée.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
      sender: 102,
      read: true
    },
    {
      id: 2002,
      text: 'Votre demande de certificat a été approuvée.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(),
      sender: 102,
      read: true
    },
    {
      id: 2003,
      text: 'Merci beaucoup pour cette bonne nouvelle!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
      sender: 'me',
      read: true
    }
  ],
  3: [
    {
      id: 3001,
      text: 'Le prochain cours aura lieu mardi à 14h. N\'oubliez pas de préparer vos questions.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
      sender: 103,
      read: true
    },
    {
      id: 3002,
      text: 'J\'y serai, merci pour le rappel.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 23).toISOString(),
      sender: 'me',
      read: true
    }
  ],
  4: [
    {
      id: 4001,
      text: 'Bonjour, j\'ai un problème avec l\'accès à mon cours en ligne.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 50).toISOString(),
      sender: 'me',
      read: true
    },
    {
      id: 4002,
      text: 'Bonjour, nous allons examiner ce problème immédiatement.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 49).toISOString(),
      sender: 104,
      read: true
    },
    {
      id: 4003,
      text: 'Votre problème a été résolu. N\'hésitez pas à nous contacter si vous avez d\'autres questions.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(),
      sender: 104,
      read: true
    }
  ],
  5: [
    {
      id: 5001,
      text: 'Est-ce que tu as les notes du cours d\'hier?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 72).toISOString(),
      sender: 105,
      read: true
    },
    {
      id: 5002,
      text: 'Oui, je vais te les envoyer ce soir.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 71).toISOString(),
      sender: 'me',
      read: true
    }
  ]
};

/**
 * Récupère la liste des conversations
 * @returns {Promise<Array>} Liste des conversations
 */
const getConversations = async () => {
  try {
    // Appel à l'API réelle
    const response = await api.get('/conversations');

    // Si la réponse est au format attendu, on la retourne directement
    if (response.data && (response.data.conversations || response.data.data)) {
      return response.data.conversations || response.data.data || [];
    }

    // Si la réponse n'est pas au format attendu, on la retourne telle quelle
    return Array.isArray(response.data) ? response.data : [];
  } catch (error) {
    console.error("Erreur lors de la récupération des conversations:", error);
    throw new Error("Impossible de récupérer vos conversations. Veuillez réessayer plus tard.");
  }
};

/**
 * Récupère les messages d'une conversation
 * @param {number} conversationId - ID de la conversation
 * @returns {Promise<Array>} Liste des messages
 */
const getMessages = async (conversationId) => {
  try {
    // Appel à l'API réelle
    const response = await api.get(`/conversations/${conversationId}/messages`);

    // Si la réponse est au format attendu, on la retourne directement
    if (response.data && (response.data.messages || response.data.data)) {
      return response.data.messages || response.data.data || [];
    }

    // Si la réponse n'est pas au format attendu, on la retourne telle quelle
    return Array.isArray(response.data) ? response.data : [];
  } catch (error) {
    console.error("Erreur lors de la récupération des messages:", error);
    throw new Error("Impossible de récupérer les messages. Veuillez réessayer plus tard.");
  }
};

/**
 * Envoie un message
 * @param {number} conversationId - ID de la conversation
 * @param {string} text - Contenu du message
 * @returns {Promise<Object>} Message envoyé
 */
const sendMessage = async (conversationId, text) => {
  try {
    // Appel à l'API réelle
    const response = await api.post(`/conversations/${conversationId}/messages`, {
      content: text,
      // Ajoutez d'autres champs si nécessaire selon votre API
    });

    return response.data;
  } catch (error) {
    console.error("Erreur lors de l'envoi du message:", error);
    throw new Error("Impossible d'envoyer le message. Veuillez réessayer plus tard.");
  }
};

/**
 * Marque tous les messages d'une conversation comme lus
 * @param {number} conversationId - ID de la conversation
 * @returns {Promise<Object>} Résultat de l'opération
 */
const markConversationAsRead = async (conversationId) => {
  try {
    // Appel à l'API réelle
    const response = await api.put(`/conversations/${conversationId}/read`);
    return response.data;
  } catch (error) {
    console.error("Erreur lors du marquage de la conversation comme lue:", error);
    throw new Error("Impossible de marquer la conversation comme lue. Veuillez réessayer plus tard.");
  }
};

/**
 * Formate le temps relatif pour l'affichage
 * @param {string} dateString - Date au format ISO
 * @returns {string} - Temps relatif formaté
 */
const formatMessageTime = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffSec < 60) {
    return "À l'instant";
  } else if (diffMin < 60) {
    return `Il y a ${diffMin} min`;
  } else if (diffHour < 24) {
    return `Il y a ${diffHour}h`;
  } else if (diffDay < 7) {
    return `Il y a ${diffDay}j`;
  } else {
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short'
    });
  }
};

const messageService = {
  getConversations,
  getMessages,
  sendMessage,
  markConversationAsRead,
  formatMessageTime
};

export default messageService;
