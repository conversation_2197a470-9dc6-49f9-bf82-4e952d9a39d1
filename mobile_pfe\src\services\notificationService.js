import api from './api';

/**
 * Types de notifications
 */
export const NOTIFICATION_TYPES = {
  MESSAGE: "message",
  RECLAMATION: "reclamation",
  CERTIFICAT: "certificat",
  EVALUATION: "evaluation",
  EVENEMENT: "evenement",
  SYSTEM: "system",
};

/**
 * Détermine le type de notification en fonction de son contenu
 * @param {Object} notification - Objet notification
 * @returns {string} - Type de notification
 */
export const getNotificationType = (notification) => {
  const content = notification.Description?.toLowerCase() || '';

  if (content.includes('message') || content.includes('envoyé')) {
    return NOTIFICATION_TYPES.MESSAGE;
  } else if (content.includes('réclamation') || content.includes('ticket')) {
    return NOTIFICATION_TYPES.RECLAMATION;
  } else if (content.includes('certificat') || content.includes('diplôme')) {
    return NOTIFICATION_TYPES.CERTIFICAT;
  } else if (content.includes('évaluation') || content.includes('quiz') || content.includes('test')) {
    return NOTIFICATION_TYPES.EVALUATION;
  } else if (content.includes('événement') || content.includes('rendez-vous')) {
    return NOTIFICATION_TYPES.EVENEMENT;
  }

  return NOTIFICATION_TYPES.SYSTEM;
};

/**
 * Récupère les notifications de l'utilisateur connecté
 * @param {Object} options - Options de requête
 * @param {number} [options.limit] - Nombre maximum de notifications à récupérer
 * @param {boolean} [options.unreadOnly] - Si true, récupère uniquement les notifications non lues
 * @returns {Promise<Object>} - Objet contenant les notifications et le nombre de notifications non lues
 */
const getNotifications = async (options = {}) => {
  try {
    console.log('Getting notifications from real API');

    // Use the same endpoint as frontend_pfe
    const response = await api.get('/notification', { params: options });

    console.log('Notifications API response:', response.data);

    // Handle different response formats (same as frontend)
    const notifications = response.data?.['hydra:member'] || response.data?.notifications || [];
    const unreadCount = response.data?.unreadCount || 0;

    return {
      notifications: Array.isArray(notifications) ? notifications : [],
      unreadCount: unreadCount
    };
  } catch (error) {
    console.error('Error fetching notifications:', error);

    if (error.response?.status === 401) {
      throw new Error('Session expirée, veuillez vous reconnecter');
    }

    throw new Error('Impossible de récupérer les notifications');
  }
};

/**
 * Marque une notification comme lue
 * @param {number} notificationId - ID de la notification
 * @returns {Promise<Object>} - Résultat de l'opération
 */
const markAsRead = async (notificationId) => {
  try {
    console.log(`Marking notification ${notificationId} as read`);

    // Use the same endpoint as frontend_pfe
    const response = await api.put(`/notification/${notificationId}/read`);

    console.log('Mark as read API response:', response.data);

    return {
      success: true,
      message: 'Notification marquée comme lue'
    };
  } catch (error) {
    console.error('Error marking notification as read:', error);

    if (error.response?.status === 401) {
      throw new Error('Session expirée, veuillez vous reconnecter');
    }

    throw new Error('Impossible de marquer la notification comme lue');
  }
};

/**
 * Marque toutes les notifications comme lues
 * @returns {Promise<Object>} - Résultat de l'opération
 */
const markAllAsRead = async () => {
  try {
    console.log('Marking all notifications as read');

    // Use the same endpoint as frontend_pfe
    const response = await api.put('/notification/mark-all-read');

    console.log('Mark all as read API response:', response.data);

    return {
      success: true,
      message: 'Toutes les notifications ont été marquées comme lues'
    };
  } catch (error) {
    console.error('Error marking all notifications as read:', error);

    if (error.response?.status === 401) {
      throw new Error('Session expirée, veuillez vous reconnecter');
    }

    throw new Error('Impossible de marquer toutes les notifications comme lues');
  }
};

/**
 * Formate le temps relatif pour l'affichage
 * @param {string} dateString - Date au format ISO
 * @returns {string} - Temps relatif formaté
 */
const formatRelativeTime = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffSec < 60) {
    return "À l'instant";
  } else if (diffMin < 60) {
    return `Il y a ${diffMin} minute${diffMin > 1 ? 's' : ''}`;
  } else if (diffHour < 24) {
    return `Il y a ${diffHour} heure${diffHour > 1 ? 's' : ''}`;
  } else if (diffDay < 7) {
    return `Il y a ${diffDay} jour${diffDay > 1 ? 's' : ''}`;
  } else {
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  }
};

const notificationService = {
  getNotifications,
  markAsRead,
  markAllAsRead,
  formatRelativeTime,
  getNotificationType
};

export default notificationService;
