import React, { useContext } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { ThemeContext } from '../contexts/ThemeContext';

const ThemeToggle = ({ style }) => {
  const { isDarkMode, toggleTheme, theme } = useContext(ThemeContext);

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: theme.card }, style]}
      onPress={toggleTheme}
      activeOpacity={0.7}
    >
      <MaterialCommunityIcons
        name={isDarkMode ? 'weather-night' : 'weather-sunny'}
        size={24}
        color={isDarkMode ? theme.accent : theme.primary}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
});

export default ThemeToggle;
