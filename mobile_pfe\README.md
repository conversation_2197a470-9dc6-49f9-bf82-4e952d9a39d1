# Mobile PFE - Apprenant Interface

This is a mobile version of the apprenant interface from the web frontend, fully connected to the backend_pfe API.

## Features

- Complete mobile UI for the apprenant interface
- **Real backend integration** with backend_pfe (https://127.0.0.1:8000/api)
- **JWT Authentication** (same as frontend_pfe)
- **WebSocket support** for real-time notifications
- Includes all main screens:
  - Dashboard (with real-time stats)
  - Courses & Course Details
  - Messaging (with real-time chat)
  - Reclamations
  - Certificates
  - Profile

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- Expo CLI
- Expo Go app installed on your iPhone SE 2020

### Installation

1. Clone the repository
2. Navigate to the mobile_pfe directory
3. Install dependencies:

```bash
npm install
# or
yarn install
```

### Running the App

1. Start the Expo development server:

```bash
npm start
# or
yarn start
```

2. Scan the QR code with the Expo Go app on your iPhone SE 2020

## Project Structure

- `assets/` - Contains images and other static assets
- `src/` - Source code
  - `components/` - Reusable UI components
  - `contexts/` - React contexts (AuthContext)
  - `data/` - Mock data
  - `navigation/` - Navigation configuration
  - `screens/` - Screen components
  - `services/` - Service modules (API, course service, etc.)

## Backend Integration

The app is now fully connected to the backend_pfe API:

### API Services
- **Authentication**: JWT-based login/logout (same as frontend_pfe)
- **Courses**: Real course data, progress tracking, quiz submissions
- **Messaging**: Real-time messaging with formateurs
- **Reclamations**: Create and manage support tickets
- **Certificates**: Generate and view certificates
- **Notifications**: Real-time notifications via WebSocket
- **Dashboard**: Live statistics and activity feeds

### WebSocket Features
- Real-time notifications
- Live message updates
- Connection status monitoring
- Automatic reconnection

### Security
- JWT token authentication
- Automatic token refresh
- Secure HTTPS connections
- Session management

## Notes

- **Backend Required**: The backend_pfe server must be running on https://127.0.0.1:8000
- **WebSocket Server**: The WebSocket server should be running on wss://127.0.0.1:8080
- **Authentication**: Users must log in with valid credentials
- **Real Data**: All data comes from the actual database

## Troubleshooting

- If you encounter issues with Expo Go, try clearing the cache:
  ```bash
  expo start -c
  ```

- If the app doesn't load on your device, make sure your device and computer are on the same network

- For other issues, check the Expo documentation or open an issue in the repository
