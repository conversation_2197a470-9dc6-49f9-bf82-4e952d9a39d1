import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';

const CourseCard = ({ course, onPress }) => {
  // Default image if none provided
  const courseImage = course.image 
    ? { uri: course.image } 
    : require('../../assets/default-course.png');
  
  // Calculate progress percentage
  const progressPercentage = course.progress ? `${course.progress}%` : '0%';
  
  return (
    <TouchableOpacity 
      style={styles.card}
      onPress={() => onPress(course)}
      activeOpacity={0.7}
    >
      <Image source={courseImage} style={styles.image} />
      
      <View style={styles.content}>
        {course.category && (
          <View style={styles.categoryContainer}>
            <Text style={styles.category}>{course.category}</Text>
          </View>
        )}
        
        <Text style={styles.title} numberOfLines={2}>
          {course.titre || course.title}
        </Text>
        
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: progressPercentage }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>{progressPercentage}</Text>
        </View>
        
        <View style={styles.footer}>
          <View style={styles.footerItem}>
            <MaterialCommunityIcons name="book-open-variant" size={16} color="#6B7280" />
            <Text style={styles.footerText}>
              {course.modules ? `${course.modules.length} modules` : 'Modules'}
            </Text>
          </View>
          
          {course.duration && (
            <View style={styles.footerItem}>
              <MaterialCommunityIcons name="clock-outline" size={16} color="#6B7280" />
              <Text style={styles.footerText}>{course.duration}</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  image: {
    width: '100%',
    height: 150,
    resizeMode: 'cover',
  },
  content: {
    padding: 16,
  },
  categoryContainer: {
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  category: {
    color: '#4F46E5',
    fontSize: 12,
    fontWeight: '500',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 12,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    marginRight: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4F46E5',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#4F46E5',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footerItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerText: {
    marginLeft: 4,
    fontSize: 12,
    color: '#6B7280',
  },
});

export default CourseCard;
