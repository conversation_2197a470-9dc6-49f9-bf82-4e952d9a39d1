import React, { useState, useEffect, useContext, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';
import { AuthContext } from '../../contexts/AuthContext';
import { ThemeContext } from '../../contexts/ThemeContext';
import messagerieService from '../../services/messagerieService';
import MessageItem from '../../components/MessageItem';
import LoadingScreen from '../../components/LoadingScreen';

const ConversationDetailScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { conversationId, name, avatar } = route.params;

  const { user } = useContext(AuthContext);
  const { theme, isDarkMode } = useContext(ThemeContext);
  const [loading, setLoading] = useState(true);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const [error, setError] = useState(null);

  const messagesListRef = useRef(null);

  // Set the header title
  useEffect(() => {
    navigation.setOptions({
      title: name || 'Conversation',
      headerRight: () => (
        <TouchableOpacity
          style={{ marginRight: 16 }}
          onPress={() => {
            // Could add functionality to view contact info or settings
            Alert.alert('Info', 'Fonctionnalité à implémenter');
          }}
        >
          <MaterialCommunityIcons name="information-outline" size={24} color={theme.text.primary} />
        </TouchableOpacity>
      ),
    });
  }, [navigation, name, theme]);

  // Fetch messages on component mount
  useEffect(() => {
    fetchMessages();
  }, [conversationId]);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      setError(null);
      const messagesData = await messagerieService.getMessages(conversationId);

      // Process messages to match the expected format - FIXED: Use correct fields from messaging service
      const processedMessages = messagesData.map(msg => ({
        id: msg.id,
        content: msg.content || msg.message, // Backend uses 'message' field
        isMe: msg.isMe, // Already set correctly by messaging service
        time: msg.time, // Already formatted by messaging service
        type: msg.isFile ? 'file' : 'text',
        fileName: msg.fileName,
        avatar: avatar,
        isRead: msg.isRead // Include read status
      }));

      setMessages(processedMessages);

      // Mark messages as read - FIXED: Use correct field names and logic
      const unreadMessageIds = messagesData
        .filter(msg => !msg.isMe && !msg.isRead) // Only mark formateur messages that are unread
        .map(msg => msg.id);

      if (unreadMessageIds.length > 0) {
        await messagerieService.markAsRead(unreadMessageIds);
      }
    } catch (err) {
      console.error('Error fetching messages:', err);
      setError('Impossible de charger les messages');
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    const messageContent = newMessage.trim();
    console.log('Attempting to send message:', messageContent, 'to formateur:', conversationId);

    try {
      setSendingMessage(true);

      // Optimistic update - add message to UI immediately
      const tempId = Date.now();
      const tempMessage = {
        id: tempId,
        content: messageContent,
        isMe: true,
        time: formatTime(new Date()),
        type: 'text'
      };

      setMessages(prevMessages => [...prevMessages, tempMessage]);
      setNewMessage('');

      // Scroll to bottom
      if (messagesListRef.current) {
        messagesListRef.current.scrollToEnd({ animated: true });
      }

      // Send to server - FIXED: Add better error handling and logging
      console.log('Calling messagerieService.sendMessage with formateurId:', conversationId);
      const response = await messagerieService.sendMessage(conversationId, messageContent);
      console.log('Send message response:', response);

      // Refresh messages to get the actual message from server
      await fetchMessages();
    } catch (err) {
      console.error('Error sending message:', err);
      console.error('Error details:', err.message);

      // Remove the optimistic message on error
      setMessages(prevMessages => prevMessages.filter(msg => msg.id !== tempId));

      // Show more specific error message
      const errorMessage = err.message || 'Impossible d\'envoyer le message';
      Alert.alert('Erreur', errorMessage);
    } finally {
      setSendingMessage(false);
    }
  };

  // Format time for display
  const formatTime = (timestamp) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (loading) {
    return <LoadingScreen message="Chargement des messages..." />;
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      {error ? (
        <View style={styles.errorContainer}>
          <MaterialCommunityIcons name="alert-circle" size={48} color={theme.danger} />
          <Text style={[styles.errorText, { color: theme.text.primary }]}>{error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.primary }]}
            onPress={fetchMessages}
          >
            <Text style={[styles.retryButtonText, { color: theme.text.inverse }]}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          <FlatList
            ref={messagesListRef}
            data={messages}
            keyExtractor={(item) => item.id.toString()}
            renderItem={({ item, index }) => {
              // Check if previous message is from the same sender
              const prevMsg = index > 0 ? messages[index - 1] : null;
              const showAvatar = !item.isMe && (!prevMsg || prevMsg.isMe !== item.isMe);

              return (
                <MessageItem
                  message={item}
                  isMe={item.isMe}
                  showAvatar={showAvatar}
                  time={item.time}
                  isFile={item.type === 'file'}
                  onFilePress={() => {
                    // Handle file press
                    Alert.alert('Info', 'Téléchargement de fichier à implémenter');
                  }}
                />
              );
            }}
            contentContainerStyle={styles.messagesList}
            ListEmptyComponent={
              <View style={styles.emptyMessagesContainer}>
                <MaterialCommunityIcons name="message-text" size={48} color={theme.text.tertiary} />
                <Text style={[styles.emptyMessagesText, { color: theme.text.primary }]}>
                  Aucun message dans cette conversation
                </Text>
                <Text style={[styles.emptyMessagesSubtext, { color: theme.text.secondary }]}>
                  Envoyez un message pour commencer la conversation
                </Text>
              </View>
            }
            onContentSizeChange={() =>
              messagesListRef.current?.scrollToEnd({ animated: false })
            }
          />

          <View style={[styles.inputContainer, {
            backgroundColor: theme.card,
            borderTopColor: theme.border
          }]}>
            <TextInput
              style={[styles.input, {
                backgroundColor: isDarkMode ? theme.background : '#F3F4F6',
                color: theme.text.primary
              }]}
              placeholder="Écrivez votre message..."
              placeholderTextColor={theme.text.tertiary}
              value={newMessage}
              onChangeText={setNewMessage}
              multiline
            />

            <TouchableOpacity
              style={[styles.sendButton, {
                backgroundColor: theme.primary,
                opacity: !newMessage.trim() || sendingMessage ? 0.7 : 1
              }]}
              onPress={handleSendMessage}
              disabled={!newMessage.trim() || sendingMessage}
            >
              {sendingMessage ? (
                <ActivityIndicator size="small" color={theme.text.inverse} />
              ) : (
                <MaterialCommunityIcons name="send" size={20} color={theme.text.inverse} />
              )}
            </TouchableOpacity>
          </View>
        </>
      )}
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  messagesList: {
    paddingVertical: 16,
  },
  emptyMessagesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyMessagesText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  emptyMessagesSubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderTopWidth: 1,
  },
  input: {
    flex: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 16,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ConversationDetailScreen;
