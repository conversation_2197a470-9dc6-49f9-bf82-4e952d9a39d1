import React, { useState, useContext, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  Alert,
  ActivityIndicator,
  Switch
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { AuthContext } from '../../contexts/AuthContext';
import { ThemeContext } from '../../contexts/ThemeContext';
import api from '../../services/api';
import LoadingScreen from '../../components/LoadingScreen';
import ThemeToggle from '../../components/ThemeToggle';

const ProfileScreen = () => {
  const { user, logout } = useContext(AuthContext);
  const { isDarkMode, toggleTheme, theme } = useContext(ThemeContext);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [userData, setUserData] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [error, setError] = useState(null);

  // Form fields
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const fetchUserProfile = async () => {
    try {
      setError(null);
      console.log('Fetching user profile from real API');

      // Get user profile from real API
      const response = await api.get('/user/me');

      console.log('User profile API response:', response.data);

      // FIXED: Extract user data from nested response structure
      const realUserData = response.data.user || response.data;
      console.log('Extracted user data:', realUserData);

      setUserData(realUserData);

      // Set form fields with real data
      setName(realUserData.name || realUserData.nom || '');
      setEmail(realUserData.email || '');
      setPhone(realUserData.phone || realUserData.telephone || '');

      console.log('Form fields set:', {
        name: realUserData.name || realUserData.nom,
        email: realUserData.email,
        phone: realUserData.phone || realUserData.telephone
      });
    } catch (err) {
      console.error('Error fetching user profile:', err);
      setError('Impossible de charger les données du profil');
    }
  };

  const handleSaveProfile = async () => {
    console.log('🔄 Profile update started');
    console.log('📝 Current form data:', { name, email, phone, hasNewPassword: !!newPassword });

    if (!name.trim()) {
      console.log('❌ Validation failed: Name is required');
      window.alert('Erreur: Le nom est obligatoire');
      return;
    }

    if (!email.trim()) {
      window.alert('Erreur: L\'email est obligatoire');
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      window.alert('Erreur: Format d\'email invalide');
      return;
    }

    // Validate password if changing
    if (newPassword) {
      if (!currentPassword) {
        window.alert('Erreur: Le mot de passe actuel est requis pour changer de mot de passe');
        return;
      }

      if (newPassword.length < 8) {
        window.alert('Erreur: Le nouveau mot de passe doit contenir au moins 8 caractères');
        return;
      }

      if (newPassword !== confirmPassword) {
        window.alert('Erreur: Les mots de passe ne correspondent pas');
        return;
      }
    }

    try {
      setSaving(true);
      console.log('✅ Validation passed - saving profile with real API');

      const updateData = {
        name: name.trim(),
        email: email.trim(),
        phone: phone.trim(),
      };

      if (newPassword) {
        updateData.currentPassword = currentPassword;
        updateData.newPassword = newPassword;
        console.log('🔐 Password change requested');
      }

      console.log('📤 Profile data to update:', updateData);
      console.log('🎯 API endpoint:', `/user/${userData.id}`);

      // Use real API to update profile
      const response = await api.put(`/user/${userData.id}`, updateData);

      console.log('📥 Profile update API response:', response.data);
      console.log('✅ Profile update successful');

      // Reset password fields
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');

      // Exit edit mode
      setEditMode(false);

      // Update local user data with response from server
      const updatedUserData = response.data.user || response.data;
      setUserData(updatedUserData);

      // Update form fields with new data
      setName(updatedUserData.name || '');
      setEmail(updatedUserData.email || '');
      setPhone(updatedUserData.phone || '');

      window.alert('Succès: Profil mis à jour avec succès');
    } catch (err) {
      console.error('Error updating profile:', err);

      let errorMessage = 'Impossible de mettre à jour le profil';

      if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.response?.status === 401) {
        errorMessage = 'Session expirée, veuillez vous reconnecter';
      } else if (err.response?.status === 400) {
        errorMessage = 'Données invalides, veuillez vérifier vos informations';
      }

      window.alert('Erreur: ' + errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleLogout = () => {
    console.log('🚪 ProfileScreen logout button pressed');

    // FIXED: Use window.confirm for React Native Web compatibility
    const confirmed = window.confirm('Êtes-vous sûr de vouloir vous déconnecter ?');

    if (confirmed) {
      console.log('✅ ProfileScreen logout confirmed - calling logout function');
      logout();
    } else {
      console.log('❌ ProfileScreen logout cancelled');
    }
  };

  useEffect(() => {
    const loadUserProfile = async () => {
      setLoading(true);
      await fetchUserProfile();
      setLoading(false);
    };

    loadUserProfile();
  }, []);

  if (loading) {
    return <LoadingScreen message="Chargement du profil..." />;
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.background }]}>
      {error ? (
        <View style={[styles.errorContainer, { backgroundColor: theme.card }]}>
          <MaterialCommunityIcons name="alert-circle" size={48} color={theme.danger} />
          <Text style={[styles.errorText, { color: theme.text.primary }]}>{error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.primary }]}
            onPress={fetchUserProfile}
          >
            <Text style={[styles.retryButtonText, { color: theme.text.inverse }]}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          {/* Profile Header */}
          <View style={[styles.profileHeader, { backgroundColor: theme.card, borderBottomColor: theme.border }]}>
            <View style={styles.profileImageContainer}>
              <Image
                source={
                  userData?.profileImage
                    ? { uri: userData.profileImage }
                    : require('../../../assets/default-avatar.png')
                }
                style={styles.profileImage}
              />
              {editMode && (
                <TouchableOpacity style={[styles.editImageButton, { backgroundColor: theme.primary, borderColor: theme.card }]}>
                  <MaterialCommunityIcons name="camera" size={20} color={theme.text.inverse} />
                </TouchableOpacity>
              )}
            </View>

            <Text style={[styles.profileName, { color: theme.text.primary }]}>{userData?.name || userData?.nom || 'Utilisateur'}</Text>
            <Text style={[styles.profileRole, { color: theme.text.secondary }]}>Apprenant</Text>

            {!editMode && (
              <TouchableOpacity
                style={[styles.editButton, { backgroundColor: theme.primary }]}
                onPress={() => setEditMode(true)}
              >
                <MaterialCommunityIcons name="pencil" size={16} color={theme.text.inverse} />
                <Text style={[styles.editButtonText, { color: theme.text.inverse }]}>Modifier le profil</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Profile Form */}
          <View style={[styles.formContainer, { backgroundColor: theme.card }]}>
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: theme.text.primary }]}>Nom complet</Text>
              <TextInput
                style={[
                  styles.formInput,
                  {
                    backgroundColor: isDarkMode ? theme.background : '#F3F4F6',
                    borderColor: theme.border,
                    color: theme.text.primary
                  },
                  !editMode && styles.formInputDisabled
                ]}
                value={name}
                onChangeText={setName}
                editable={editMode}
                placeholderTextColor={theme.text.tertiary}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: theme.text.primary }]}>Email</Text>
              <TextInput
                style={[
                  styles.formInput,
                  {
                    backgroundColor: isDarkMode ? theme.background : '#F3F4F6',
                    borderColor: theme.border,
                    color: theme.text.primary
                  },
                  !editMode && styles.formInputDisabled
                ]}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                editable={editMode}
                placeholderTextColor={theme.text.tertiary}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: theme.text.primary }]}>Téléphone</Text>
              <TextInput
                style={[
                  styles.formInput,
                  {
                    backgroundColor: isDarkMode ? theme.background : '#F3F4F6',
                    borderColor: theme.border,
                    color: theme.text.primary
                  },
                  !editMode && styles.formInputDisabled
                ]}
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
                editable={editMode}
                placeholderTextColor={theme.text.tertiary}
              />
            </View>

            {editMode && (
              <>
                <View style={[styles.passwordSection, { borderTopColor: theme.border }]}>
                  <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Changer le mot de passe</Text>

                  <View style={styles.formGroup}>
                    <Text style={[styles.formLabel, { color: theme.text.primary }]}>Mot de passe actuel</Text>
                    <TextInput
                      style={[
                        styles.formInput,
                        {
                          backgroundColor: isDarkMode ? theme.background : '#F3F4F6',
                          borderColor: theme.border,
                          color: theme.text.primary
                        }
                      ]}
                      value={currentPassword}
                      onChangeText={setCurrentPassword}
                      secureTextEntry
                      placeholderTextColor={theme.text.tertiary}
                    />
                  </View>

                  <View style={styles.formGroup}>
                    <Text style={[styles.formLabel, { color: theme.text.primary }]}>Nouveau mot de passe</Text>
                    <TextInput
                      style={[
                        styles.formInput,
                        {
                          backgroundColor: isDarkMode ? theme.background : '#F3F4F6',
                          borderColor: theme.border,
                          color: theme.text.primary
                        }
                      ]}
                      value={newPassword}
                      onChangeText={setNewPassword}
                      secureTextEntry
                      placeholderTextColor={theme.text.tertiary}
                    />
                  </View>

                  <View style={styles.formGroup}>
                    <Text style={[styles.formLabel, { color: theme.text.primary }]}>Confirmer le mot de passe</Text>
                    <TextInput
                      style={[
                        styles.formInput,
                        {
                          backgroundColor: isDarkMode ? theme.background : '#F3F4F6',
                          borderColor: theme.border,
                          color: theme.text.primary
                        }
                      ]}
                      value={confirmPassword}
                      onChangeText={setConfirmPassword}
                      secureTextEntry
                      placeholderTextColor={theme.text.tertiary}
                    />
                  </View>
                </View>

                <View style={styles.formActions}>
                  <TouchableOpacity
                    style={[styles.cancelButton, { backgroundColor: isDarkMode ? theme.background : '#F3F4F6' }]}
                    onPress={() => {
                      setEditMode(false);
                      // Reset form to original values
                      setName(userData?.name || userData?.nom || '');
                      setEmail(userData?.email || '');
                      setPhone(userData?.phone || userData?.telephone || '');
                      setCurrentPassword('');
                      setNewPassword('');
                      setConfirmPassword('');
                    }}
                  >
                    <Text style={[styles.cancelButtonText, { color: theme.text.secondary }]}>Annuler</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.saveButton,
                      { backgroundColor: theme.primary },
                      saving && styles.saveButtonDisabled
                    ]}
                    onPress={handleSaveProfile}
                    disabled={saving}
                  >
                    {saving ? (
                      <ActivityIndicator size="small" color={theme.text.inverse} />
                    ) : (
                      <Text style={[styles.saveButtonText, { color: theme.text.inverse }]}>Enregistrer</Text>
                    )}
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>

          {/* Logout Button */}
          {!editMode && (
            <View style={[styles.settingsContainer, { backgroundColor: theme.card }]}>
              <TouchableOpacity
                style={styles.logoutButton}
                onPress={handleLogout}
              >
                <MaterialCommunityIcons name="logout" size={24} color={theme.danger} />
                <Text style={[styles.logoutButtonText, { color: theme.danger }]}>Déconnexion</Text>
              </TouchableOpacity>
            </View>
          )}
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  profileHeader: {
    alignItems: 'center',
    padding: 24,
    borderBottomWidth: 1,
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  profileRole: {
    fontSize: 14,
    marginBottom: 16,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  editButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },

  formContainer: {
    padding: 16,
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  formInput: {
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    fontSize: 16,
  },
  formInputDisabled: {
    opacity: 0.7,
  },
  passwordSection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
    gap: 12,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  settingsContainer: {
    padding: 16,
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingText: {
    fontSize: 16,
    marginLeft: 12,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 24,
  },
  logoutButtonText: {
    fontSize: 16,
    marginLeft: 12,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    marginTop: 48,
  },
  errorText: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default ProfileScreen;