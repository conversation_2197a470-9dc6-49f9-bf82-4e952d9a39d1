import React, { useState, useContext, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Image,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator
} from 'react-native';
import { AuthContext } from '../../contexts/AuthContext';
import { ThemeContext, lightTheme } from '../../contexts/ThemeContext';
import { MaterialCommunityIcons } from 'react-native-vector-icons';

const LoginScreen = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState(null);
  const [currentTheme, setCurrentTheme] = useState(lightTheme);

  const { login } = useContext(AuthContext);
  const themeContext = useContext(ThemeContext);

  // Use the theme from context if available, otherwise use the default light theme
  useEffect(() => {
    if (themeContext && themeContext.theme) {
      setCurrentTheme(themeContext.theme);
    }
  }, [themeContext]);

  const handleLogin = async () => {
    // Clear previous errors
    setLoginError(null);

    // Validate inputs
    if (!email || !password) {
      setLoginError('Veuillez remplir tous les champs');
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setLoginError('Veuillez entrer une adresse email valide');
      return;
    }

    try {
      setIsLoading(true);
      const success = await login(email, password);

      if (!success) {
        setLoginError('Identifiants incorrects. Veuillez réessayer.');
      }
    } catch (error) {
      setLoginError(error.message || 'Une erreur est survenue lors de la connexion');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[styles.container, { backgroundColor: currentTheme.background }]}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.logoContainer}>
          <Image
            source={require('../../../assets/logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={[styles.appName, { color: currentTheme.primary }]}>PharmaLearn</Text>
        </View>

        <Text style={[styles.title, { color: currentTheme.text.primary }]}>Connexion</Text>
        <Text style={[styles.subtitle, { color: currentTheme.text.secondary }]}>
          Connectez-vous à votre compte pour accéder à vos cours
        </Text>

        {loginError && (
          <View style={[styles.errorContainer, { backgroundColor: currentTheme.danger + '20' }]}>
            <MaterialCommunityIcons name="alert-circle" size={20} color={currentTheme.danger} />
            <Text style={[styles.errorText, { color: currentTheme.danger }]}>{loginError}</Text>
          </View>
        )}

        <View style={styles.form}>
          <View style={[styles.inputContainer, {
            backgroundColor: currentTheme.card,
            borderColor: currentTheme.border
          }]}>
            <MaterialCommunityIcons
              name="email-outline"
              size={24}
              color={currentTheme.text.tertiary}
              style={styles.inputIcon}
            />
            <TextInput
              style={[styles.input, { color: currentTheme.text.primary }]}
              placeholder="Email"
              placeholderTextColor={currentTheme.text.tertiary}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={[styles.inputContainer, {
            backgroundColor: currentTheme.card,
            borderColor: currentTheme.border
          }]}>
            <MaterialCommunityIcons
              name="lock-outline"
              size={24}
              color={currentTheme.text.tertiary}
              style={styles.inputIcon}
            />
            <TextInput
              style={[styles.input, { color: currentTheme.text.primary }]}
              placeholder="Mot de passe"
              placeholderTextColor={currentTheme.text.tertiary}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
            />
            <TouchableOpacity
              onPress={() => setShowPassword(!showPassword)}
              style={styles.passwordToggle}
            >
              <MaterialCommunityIcons
                name={showPassword ? "eye-off-outline" : "eye-outline"}
                size={24}
                color={currentTheme.text.tertiary}
              />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.forgotPassword}
            onPress={() => navigation.navigate('ForgotPassword')}
          >
            <Text style={[styles.forgotPasswordText, { color: currentTheme.primary }]}>Mot de passe oublié ?</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.loginButton,
              { backgroundColor: currentTheme.primary },
              isLoading && styles.loginButtonDisabled
            ]}
            onPress={handleLogin}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#FFFFFF" size="small" />
            ) : (
              <Text style={styles.loginButtonText}>Se connecter</Text>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.registerContainer}>
          <Text style={[styles.registerText, { color: currentTheme.text.secondary }]}>Vous n'avez pas de compte ?</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Register')}>
            <Text style={[styles.registerLink, { color: currentTheme.primary }]}>S'inscrire</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={[styles.testApiButton, { backgroundColor: currentTheme.card, borderColor: currentTheme.border }]}
          onPress={() => navigation.navigate('TestApi')}
        >
          <Text style={[styles.testApiButtonText, { color: currentTheme.text.secondary }]}>Tester la connexion API</Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 24,
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  logo: {
    width: 100,
    height: 100,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 24,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    marginLeft: 8,
    flex: 1,
  },
  form: {
    marginBottom: 24,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    marginBottom: 16,
    paddingHorizontal: 16,
    borderWidth: 1,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    height: 56,
    fontSize: 16,
  },
  passwordToggle: {
    padding: 8,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    fontSize: 14,
    fontWeight: '500',
  },
  loginButton: {
    borderRadius: 12,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginButtonDisabled: {
    opacity: 0.7,
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  registerText: {
    fontSize: 14,
  },
  registerLink: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  testApiButton: {
    borderWidth: 1,
    borderRadius: 12,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  testApiButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default LoginScreen;
