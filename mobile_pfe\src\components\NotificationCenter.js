import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal,
  Alert,
  SafeAreaView,
  StatusBar
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { ThemeContext } from '../contexts/ThemeContext';
import notificationService, { NOTIFICATION_TYPES } from '../services/notificationService';
import webSocketService from '../services/webSocketService';

/**
 * Composant de centre de notifications
 * Version simplifiée pour résoudre les problèmes d'affichage
 */
const NotificationCenter = ({ onNotificationClick }) => {
  const { theme, isDarkMode } = useContext(ThemeContext);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);

  // Charger les notifications au chargement du composant
  useEffect(() => {
    fetchNotifications();

    // Initialize WebSocket for real-time notifications (disabled for now)
    // const stopListening = webSocketService.initializeRealTimeNotifications((newNotification) => {
    //   console.log('New notification received:', newNotification);

    //   // Add the new notification to the list
    //   setNotifications(prev => [newNotification, ...prev]);

    //   // Update unread count
    //   if (!newNotification.read) {
    //     setUnreadCount(prev => prev + 1);
    //   }
    // });

    // Mock function for cleanup
    const stopListening = () => {};

    // Rafraîchir les notifications toutes les 5 minutes (moins fréquent avec WebSocket)
    const interval = setInterval(fetchNotifications, 300000);

    return () => {
      clearInterval(interval);
      stopListening(); // Stop WebSocket listening
    };
  }, []);

  // Récupérer les notifications
  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const result = await notificationService.getNotifications();
      if (result && result.notifications) {
        setNotifications(result.notifications || []);
        setUnreadCount(result.unreadCount || 0);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des notifications:", error);
    } finally {
      setLoading(false);
    }
  };

  // Basculer l'affichage des notifications
  const toggleNotifications = () => {
    // Afficher une alerte pour déboguer
    console.log("Toggle notifications, current state:", showNotifications);
    Alert.alert("Notifications", "Chargement des notifications...");

    // Inverser l'état
    setShowNotifications(!showNotifications);
  };

  // Marquer une notification comme lue
  const handleNotificationClick = async (notification) => {
    try {
      await notificationService.markAsRead(notification.id);

      // Mettre à jour l'état local
      setNotifications(
        notifications.map((n) =>
          n.id === notification.id ? { ...n, read: true } : n
        )
      );

      // Mettre à jour le compteur de notifications non lues
      if (!notification.read) {
        setUnreadCount(Math.max(0, unreadCount - 1));
      }

      // Fermer le modal
      setShowNotifications(false);

      // Appeler la fonction de callback si elle existe
      if (onNotificationClick) {
        onNotificationClick(notification);
      }
    } catch (error) {
      console.error("Erreur lors du marquage de la notification comme lue:", error);
    }
  };

  // Marquer toutes les notifications comme lues
  const markAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead();

      // Mettre à jour l'état local
      setNotifications(
        notifications.map((n) => ({ ...n, read: true }))
      );

      // Mettre à jour le compteur de notifications non lues
      setUnreadCount(0);
    } catch (error) {
      console.error("Erreur lors du marquage de toutes les notifications comme lues:", error);
    }
  };

  // Obtenir l'icône pour un type de notification
  const getIconForType = (notification) => {
    const type = notificationService.getNotificationType(notification);

    switch (type) {
      case NOTIFICATION_TYPES.MESSAGE:
        return "message-text";
      case NOTIFICATION_TYPES.RECLAMATION:
        return "alert-circle";
      case NOTIFICATION_TYPES.CERTIFICAT:
        return "certificate";
      case NOTIFICATION_TYPES.EVALUATION:
        return "clipboard-check";
      case NOTIFICATION_TYPES.EVENEMENT:
        return "calendar";
      default:
        return "bell";
    }
  };

  // Obtenir la couleur pour un type de notification
  const getColorForType = (notification) => {
    const type = notificationService.getNotificationType(notification);

    switch (type) {
      case NOTIFICATION_TYPES.MESSAGE:
        return theme.info;
      case NOTIFICATION_TYPES.RECLAMATION:
        return theme.warning;
      case NOTIFICATION_TYPES.CERTIFICAT:
        return theme.success;
      case NOTIFICATION_TYPES.EVALUATION:
        return theme.primary;
      case NOTIFICATION_TYPES.EVENEMENT:
        return theme.accent;
      default:
        return theme.text.secondary;
    }
  };

  // Rendu d'une notification
  const renderNotificationItem = ({ item }) => {
    const iconName = getIconForType(item);
    const iconColor = getColorForType(item);

    return (
      <TouchableOpacity
        style={[
          styles.notificationItem,
          {
            borderBottomColor: theme.border,
            backgroundColor: item.read ? 'transparent' : isDarkMode ? `${theme.primary}10` : `${theme.primary}05`,
            borderLeftWidth: item.read ? 0 : 4,
            borderLeftColor: item.read ? 'transparent' : theme.primary
          }
        ]}
        onPress={() => handleNotificationClick(item)}
      >
        <View style={[styles.iconContainer, { backgroundColor: `${iconColor}20` }]}>
          <MaterialCommunityIcons name={iconName} size={20} color={iconColor} />
        </View>
        <View style={styles.notificationContent}>
          <Text style={[styles.notificationText, { color: theme.text.primary }]}>
            {item.Description}
          </Text>
          <Text style={[styles.notificationTime, { color: theme.text.secondary }]}>
            {notificationService.formatRelativeTime(item.createdAt)}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View>
      {/* Bouton de notification */}
      <TouchableOpacity
        style={styles.notificationButton}
        onPress={() => {
          console.log("Notification button pressed");
          toggleNotifications();
        }}
      >
        <MaterialCommunityIcons
          name="bell-outline"
          size={24}
          color={theme.text.primary}
        />
        {unreadCount > 0 && (
          <View style={[styles.badge, { backgroundColor: theme.primary }]}>
            <Text style={styles.badgeText}>{unreadCount}</Text>
          </View>
        )}
      </TouchableOpacity>

      {/* Modal de notifications - Version simplifiée */}
      <Modal
        visible={showNotifications}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowNotifications(false)}
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
          <StatusBar backgroundColor="rgba(0,0,0,0.5)" barStyle="light-content" />

          <View style={[styles.modalContent, { backgroundColor: theme.card }]}>
            {/* En-tête du modal */}
            <View style={[styles.modalHeader, { borderBottomColor: theme.border }]}>
              <Text style={[styles.modalTitle, { color: theme.text.primary }]}>
                Notifications
              </Text>
              <View style={styles.headerActions}>
                {unreadCount > 0 && (
                  <TouchableOpacity
                    style={[styles.markAllButton, { backgroundColor: theme.primary + '20' }]}
                    onPress={markAllAsRead}
                  >
                    <Text style={[styles.markAllText, { color: theme.primary }]}>
                      Tout marquer comme lu
                    </Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setShowNotifications(false)}
                >
                  <MaterialCommunityIcons name="close" size={20} color={theme.text.secondary} />
                </TouchableOpacity>
              </View>
            </View>

            {/* Liste des notifications */}
            <FlatList
              data={notifications}
              renderItem={renderNotificationItem}
              keyExtractor={(item) => item.id.toString()}
              contentContainerStyle={styles.notificationsList}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <MaterialCommunityIcons
                    name="bell-off-outline"
                    size={48}
                    color={theme.text.tertiary}
                  />
                  <Text style={[styles.emptyText, { color: theme.text.secondary }]}>
                    Aucune notification
                  </Text>
                </View>
              }
            />
          </View>
        </SafeAreaView>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  notificationButton: {
    padding: 8,
    borderRadius: 8,
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: 2,
    right: 2,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxHeight: '80%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  markAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  markAllText: {
    fontSize: 12,
    fontWeight: '500',
  },
  closeButton: {
    padding: 4,
  },
  notificationsList: {
    flexGrow: 1,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  notificationContent: {
    flex: 1,
  },
  notificationText: {
    fontSize: 14,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 12,
    fontSize: 14,
  },
});

export default NotificationCenter;
