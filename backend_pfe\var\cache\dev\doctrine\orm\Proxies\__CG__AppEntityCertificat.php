<?php

namespace Proxies\__CG__\App\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Certificat extends \App\Entity\Certificat implements \Doctrine\ORM\Proxy\InternalProxy
{
    use \Symfony\Component\VarExporter\LazyGhostTrait {
        initializeLazyObject as private;
        setLazyObjectAsInitialized as public __setInitialized;
        isLazyObjectInitialized as private;
        createLazyGhost as private;
        resetLazyObject as private;
    }

    public function __load(): void
    {
        $this->initializeLazyObject();
    }
    

    private const LAZY_OBJECT_PROPERTY_SCOPES = [
        "\0".parent::class."\0".'apprenant' => [parent::class, 'apprenant', null, 16],
        "\0".parent::class."\0".'contenu' => [parent::class, 'contenu', null, 16],
        "\0".parent::class."\0".'dateObtention' => [parent::class, 'dateObtention', null, 16],
        "\0".parent::class."\0".'id' => [parent::class, 'id', null, 16],
        "\0".parent::class."\0".'isAutoGenerated' => [parent::class, 'isAutoGenerated', null, 16],
        "\0".parent::class."\0".'notifications' => [parent::class, 'notifications', null, 16],
        "\0".parent::class."\0".'progression' => [parent::class, 'progression', null, 16],
        'apprenant' => [parent::class, 'apprenant', null, 16],
        'contenu' => [parent::class, 'contenu', null, 16],
        'dateObtention' => [parent::class, 'dateObtention', null, 16],
        'id' => [parent::class, 'id', null, 16],
        'isAutoGenerated' => [parent::class, 'isAutoGenerated', null, 16],
        'notifications' => [parent::class, 'notifications', null, 16],
        'progression' => [parent::class, 'progression', null, 16],
    ];

    public function __isInitialized(): bool
    {
        return isset($this->lazyObjectState) && $this->isLazyObjectInitialized();
    }

    public function __serialize(): array
    {
        $properties = (array) $this;
        unset($properties["\0" . self::class . "\0lazyObjectState"]);

        return $properties;
    }
}
