# Testing the Mobile App

This document provides instructions for testing the mobile app, especially the HTTPS connection.

## Testing the HTTPS Connection

1. **Launch the App**: Start the app using Expo Go on your iPhone SE 2020.

2. **Login Screen**: On the login screen, you should see two buttons at the bottom:
   - "Tester la connexion API" - Tests the API connection using the standard API service
   - "Tester la connexion HTTPS" - Tests the HTTPS connection directly

3. **Test HTTPS Connection**: Tap the "Tester la connexion HTTPS" button. This will navigate to a test screen where you can:
   - Test the API connection
   - Test the HTTPS connection directly
   - View the test results

4. **Check Results**: The test results will show whether the connection was successful or not. If there are any issues, check the error messages for more information.

## Troubleshooting

If you encounter connection issues, try the following:

1. **Check Backend Server**: Make sure your backend server is running and accessible.

2. **Check IP Address**: Verify that the IP address in the API URL is correct. The current configuration uses:
   ```
   https://*************:8000/api
   ```
   If your server is running on a different IP or port, update the URL in `src/utils/constants.js`.

3. **Certificate Issues**: If you're using a self-signed certificate, you may need to trust it on your device. See the `src/utils/README_HTTPS.md` file for more information.

4. **Network Issues**: Make sure your device is on the same network as your development machine.

5. **Console Logs**: Check the console logs for more detailed error messages.

## Next Steps

Once the HTTPS connection is working, you can proceed with testing the rest of the app's functionality:

1. **Login**: Test logging in with valid credentials.
2. **Dashboard**: Verify that the dashboard loads correctly.
3. **Courses**: Check that courses are displayed and can be accessed.
4. **Messaging**: Test the messaging functionality.
5. **Reclamations**: Test creating and viewing reclamations.
6. **Profile**: Verify that the profile information is displayed correctly.

If you encounter any issues, please report them with detailed information about the steps to reproduce the problem.
