import React, { useContext, useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
  Animated,
  Pressable,
  Alert,
  BackHandler,
} from 'react-native';
import { MaterialCommunityIcons } from 'react-native-vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ThemeContext } from '../contexts/ThemeContext';
import { AuthContext } from '../contexts/AuthContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import NotificationCenter from './NotificationCenter';

const AppHeader = ({ title, showBackButton = false, showProfileButton = true }) => {
  const { theme, isDarkMode, toggleTheme } = useContext(ThemeContext);
  const { user, logout } = useContext(AuthContext);
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const [showProfileMenu, setShowProfileMenu] = useState(false);

  // Animation for profile menu
  const menuAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(menuAnimation, {
      toValue: showProfileMenu ? 1 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [showProfileMenu]);

  const menuTranslateY = menuAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [-20, 0],
  });

  const menuOpacity = menuAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  const handleBackPress = () => {
    navigation.goBack();
  };

  const toggleProfileMenu = () => {
    setShowProfileMenu(!showProfileMenu);
  };

  // Close profile menu when clicking outside
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (showProfileMenu) {
        setShowProfileMenu(false);
        return true;
      }
      return false;
    });

    return () => backHandler.remove();
  }, [showProfileMenu]);

  const handleProfileAction = (action) => {
    console.log('🎯 Profile action triggered:', action);
    setShowProfileMenu(false);

    if (action === 'profile') {
      console.log('👤 Navigating to Profile screen');
      navigation.navigate('Profile');
    } else if (action === 'logout') {
      console.log('🚪 Logout action triggered - showing confirmation dialog');

      // FIXED: Use window.confirm for React Native Web compatibility
      const confirmed = window.confirm('Êtes-vous sûr de vouloir vous déconnecter ?');

      if (confirmed) {
        console.log('✅ User confirmed logout - calling logout function');
        logout();
      } else {
        console.log('❌ Logout cancelled by user');
      }
    }
  };

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: theme.card,
          borderBottomColor: theme.border,
          paddingTop: insets.top,
        }
      ]}
    >
      <View style={styles.headerContent}>
        {showBackButton ? (
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBackPress}
          >
            <MaterialCommunityIcons
              name="arrow-left"
              size={24}
              color={theme.text.primary}
            />
          </TouchableOpacity>
        ) : null}

        <Text style={[styles.title, { color: theme.text.primary }]}>
          {title}
        </Text>

        <View style={styles.rightContainer}>
          {/* Theme Toggle Button */}
          <TouchableOpacity
            style={styles.iconButton}
            onPress={toggleTheme}
          >
            <MaterialCommunityIcons
              name={isDarkMode ? 'weather-night' : 'weather-sunny'}
              size={24}
              color={isDarkMode ? theme.accent : theme.primary}
            />
          </TouchableOpacity>

          {/* Notifications Center */}
          <NotificationCenter
            onNotificationClick={(notification) => {
              // Gérer le clic sur une notification
              console.log('Notification clicked:', notification);

              // Naviguer vers la page appropriée en fonction du type de notification
              // Cette logique peut être adaptée selon vos besoins
              const type = notification.type || '';
              if (type.includes('message') || notification.Description.toLowerCase().includes('message')) {
                navigation.navigate('Messagerie');
              } else if (type.includes('reclamation') || notification.Description.toLowerCase().includes('réclamation')) {
                navigation.navigate('Reclamation');
              } else if (type.includes('cours') || notification.Description.toLowerCase().includes('cours')) {
                navigation.navigate('Courses');
              }
            }}
          />

          {/* Profile Button */}
          {showProfileButton && (
            <View style={styles.profileContainer}>
              <TouchableOpacity
                style={styles.profileButton}
                onPress={toggleProfileMenu}
              >
                <Image
                  source={
                    user?.profileImage
                      ? { uri: user.profileImage }
                      : require('../assets/default-avatar.png')
                  }
                  style={styles.profileImage}
                />
              </TouchableOpacity>

              {/* Profile Menu */}
              {showProfileMenu && (
                <>
                  {/* Invisible backdrop to handle outside touches */}
                  <TouchableOpacity
                    style={styles.backdrop}
                    activeOpacity={0}
                    onPress={() => setShowProfileMenu(false)}
                  />
                  <Animated.View
                    style={[
                      styles.profileMenu,
                      {
                        backgroundColor: theme.card,
                        borderColor: theme.border,
                        transform: [{ translateY: menuTranslateY }],
                        opacity: menuOpacity,
                      },
                    ]}
                  >
                  <Pressable
                    style={({ pressed }) => [
                      styles.menuItem,
                      pressed && { backgroundColor: isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' },
                    ]}
                    onPress={() => handleProfileAction('profile')}
                  >
                    <MaterialCommunityIcons name="account" size={20} color={theme.text.primary} />
                    <Text style={[styles.menuItemText, { color: theme.text.primary }]}>
                      Mon Profil
                    </Text>
                  </Pressable>

                  <Pressable
                    style={({ pressed }) => [
                      styles.menuItem,
                      pressed && { backgroundColor: isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' },
                    ]}
                    onPress={() => handleProfileAction('logout')}
                  >
                    <MaterialCommunityIcons name="logout" size={20} color={theme.danger} />
                    <Text style={[styles.menuItemText, { color: theme.danger }]}>
                      Déconnexion
                    </Text>
                  </Pressable>
                </Animated.View>
                </>
              )}
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 1,
    zIndex: 10, // Higher than content but lower than dropdown
    ...Platform.select({
      ios: {
        shadowColor: 'rgba(0,0,0,0.2)',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.5,
        shadowRadius: 1,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  headerContent: {
    height: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
    borderRadius: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    padding: 8,
    borderRadius: 8,
    marginLeft: 8,
  },
  profileContainer: {
    zIndex: 1000, // Add high z-index to ensure dropdown appears above other elements
  },
  backdrop: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    right: -1000,
    bottom: -1000,
    zIndex: 999, // Just below the dropdown but above everything else
  },
  profileButton: {
    marginLeft: 8,
    borderRadius: 20,
    overflow: 'hidden',
  },
  profileImage: {
    width: 36,
    height: 36,
    borderRadius: 18,
  },

  profileMenu: {
    position: 'absolute',
    top: 48,
    right: 0,
    width: 180,
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
    zIndex: 1000, // Add high z-index to ensure it appears above other elements
    ...Platform.select({
      ios: {
        shadowColor: 'rgba(0,0,0,0.3)',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 4,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  menuItemText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default AppHeader;
