import api from './api';

const certificateService = {
  // Get all certificates for the current user - FIXED: Use correct backend endpoint
  getUserCertificates: async () => {
    try {
      console.log('Getting user certificates from real API');

      // FIXED: Backend uses /certificat/apprenant/{apprenantId}, not /certificat
      // First get current user to get their ID
      const userResponse = await api.get('/user/me');
      const currentUser = userResponse.data;

      // Check for user ID in different possible fields
      const userId = currentUser?.id || currentUser?.userId || currentUser?.user?.id;

      if (!currentUser || !userId) {
        throw new Error('Impossible de récupérer les informations utilisateur');
      }

      // Use the correct backend endpoint
      const response = await api.get(`/certificat/apprenant/${userId}`);

      console.log('User certificates API response:', response.data);

      // Handle the correct response format from backend
      const certificates = response.data?.certificats || response.data?.['hydra:member'] || response.data || [];

      console.log('Extracted certificates:', certificates);

      return Array.isArray(certificates) ? certificates : [];
    } catch (error) {
      console.error('Error fetching user certificates:', error);

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      throw new Error('Impossible de récupérer vos certificats');
    }
  },

  // Get a specific certificate - real API implementation
  getCertificate: async (certificateId) => {
    try {
      console.log(`Getting certificate details for ID: ${certificateId}`);

      // Use the same endpoint as frontend_pfe
      const response = await api.get(`/certificat/${certificateId}`);

      console.log('Certificate details API response:', response.data);

      return response.data;
    } catch (error) {
      console.error('Error fetching certificate details:', error);

      if (error.response?.status === 404) {
        throw new Error('Certificat non trouvé');
      }

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      throw new Error('Impossible de récupérer le certificat');
    }
  },

  // Get certificate for a specific course - FIXED: Use correct backend endpoint
  getCertificateForCourse: async (courseId) => {
    try {
      console.log(`Getting certificate for course ${courseId}`);

      // FIXED: Backend uses /certificat/apprenant/{apprenantId}/cours/{coursId}
      // First get current user to get their ID
      const userResponse = await api.get('/user/me');
      const currentUser = userResponse.data;

      // Check for user ID in different possible fields
      const userId = currentUser?.id || currentUser?.userId || currentUser?.user?.id;

      if (!currentUser || !userId) {
        throw new Error('Impossible de récupérer les informations utilisateur');
      }

      // Use the correct backend endpoint
      const response = await api.get(`/certificat/apprenant/${userId}/cours/${courseId}`);

      console.log('Certificate for course API response:', response.data);

      return response.data;
    } catch (error) {
      console.error('Error fetching certificate for course:', error);

      if (error.response?.status === 404) {
        // No certificate found for this course
        return null;
      }

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      throw new Error('Impossible de vérifier le certificat pour ce cours');
    }
  },

  // Generate a certificate for a course - FIXED: Use correct backend endpoint and data format
  generateCertificate: async (courseId) => {
    try {
      console.log(`Generating certificate for course ${courseId}`);

      // FIXED: Backend expects different data format - check frontend_pfe QuizService
      const response = await api.post('/certificat/generate', {
        coursId: parseInt(courseId)  // Backend expects coursId as integer
      });

      console.log('Generate certificate API response:', response.data);

      return {
        success: true,
        message: 'Certificat généré avec succès. Veuillez utiliser la version web pour le télécharger.',
        certificate: response.data
      };
    } catch (error) {
      console.error('Error generating certificate:', error);

      if (error.response?.status === 400) {
        throw new Error('Vous n\'avez pas encore terminé ce cours');
      }

      if (error.response?.status === 409) {
        throw new Error('Vous avez déjà obtenu un certificat pour ce cours');
      }

      if (error.response?.status === 401) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      throw new Error('Impossible de générer le certificat');
    }
  }
};

export default certificateService;
